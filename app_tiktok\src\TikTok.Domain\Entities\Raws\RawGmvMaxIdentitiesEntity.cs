using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Domain.Entities.Auditing;

namespace TikTok.Entities
{
    /// <summary>
    /// Entity đại diện cho danh sách identities của GMV Max Campaign từ API endpoint riêng.
    /// Chứa thông tin chi tiết về các tài khoản TikTok có thể sử dụng cho GMV Max Campaign.
    /// </summary>
    public class RawGmvMaxIdentitiesEntity : AuditedEntity<Guid>
    {
        public RawGmvMaxIdentitiesEntity()
        {

        }

        public RawGmvMaxIdentitiesEntity(Guid id) : base(id)
        {
        }

        /// <summary>
        /// ID Business Center
        /// </summary>
        [Required]
        [StringLength(100)]
        public string BcId { get; set; }

        /// <summary>
        /// ID nhà quảng cáo
        /// </summary>
        [Required]
        [StringLength(100)]
        public string AdvertiserId { get; set; }

        /// <summary>
        /// ID danh tính TikTok
        /// </summary>
        [Required]
        [StringLength(100)]
        public string IdentityId { get; set; }

        /// <summary>
        /// Loại danh tính (BC_AUTH_TT, TTS_TT, TT_USER)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string IdentityType { get; set; }

        /// <summary>
        /// Tên hiển thị của identity
        /// </summary>
        [StringLength(500)]
        public string? DisplayName { get; set; }

        /// <summary>
        /// Tên người dùng TikTok
        /// </summary>
        [StringLength(200)]
        public string? UserName { get; set; }

        /// <summary>
        /// URL ảnh đại diện
        /// </summary>
        [StringLength(1000)]
        public string? ProfileImage { get; set; }

        /// <summary>
        /// ID Business Center được ủy quyền (chỉ có khi identity_type là BC_AUTH_TT)
        /// </summary>
        [StringLength(100)]
        public string? IdentityAuthorizedBcId { get; set; }

        /// <summary>
        /// ID TikTok Shop (chỉ có khi identity_type là TTS_TT)
        /// </summary>
        [StringLength(100)]
        public string? StoreId { get; set; }

        /// <summary>
        /// ID Business Center được ủy quyền truy cập store
        /// </summary>
        [StringLength(100)]
        public string? StoreAuthorizedBcId { get; set; }

        /// <summary>
        /// Có khả dụng cho Live GMV Max không
        /// </summary>
        public bool LiveGmvMaxAvailable { get; set; }

        /// <summary>
        /// Có khả dụng cho Product GMV Max không
        /// </summary>
        public bool ProductGmvMaxAvailable { get; set; }

        /// <summary>
        /// Có đang chạy custom shop ads không
        /// </summary>
        public bool IsRunningCustomShopAds { get; set; }

        /// <summary>
        /// Lý do không khả dụng (OCCUPIED, UNAUTHORIZED, v.v.)
        /// </summary>
        [StringLength(100)]
        public string? UnavailableReason { get; set; }

        /// <summary>
        /// Thời gian đồng bộ dữ liệu (UTC)
        /// </summary>
        [Required]
        public DateTime SyncedAt { get; set; }

        /// <summary>
        /// Kiểm tra xem entity có thay đổi so với entity khác không
        /// </summary>
        public bool HasChanged(RawGmvMaxIdentitiesEntity other)
        {
            return
            BcId != other.BcId ||
            AdvertiserId != other.AdvertiserId ||
            IdentityId != other.IdentityId ||
            IdentityType != other.IdentityType ||
            DisplayName != other.DisplayName ||
            UserName != other.UserName ||
            ProfileImage != other.ProfileImage ||
            IdentityAuthorizedBcId != other.IdentityAuthorizedBcId ||
            StoreId != other.StoreId ||
            StoreAuthorizedBcId != other.StoreAuthorizedBcId ||
            LiveGmvMaxAvailable != other.LiveGmvMaxAvailable ||
            ProductGmvMaxAvailable != other.ProductGmvMaxAvailable ||
            IsRunningCustomShopAds != other.IsRunningCustomShopAds ||
            UnavailableReason != other.UnavailableReason;
        }

        /// <summary>
        /// Cập nhật entity từ dữ liệu mới
        /// </summary>
        public void UpdateFromNewData(RawGmvMaxIdentitiesEntity newEntity)
        {
            BcId = newEntity.BcId;
            AdvertiserId = newEntity.AdvertiserId;
            IdentityId = newEntity.IdentityId;
            IdentityType = newEntity.IdentityType;
            DisplayName = newEntity.DisplayName;
            UserName = newEntity.UserName;
            ProfileImage = newEntity.ProfileImage;
            IdentityAuthorizedBcId = newEntity.IdentityAuthorizedBcId;
            StoreId = newEntity.StoreId;
            StoreAuthorizedBcId = newEntity.StoreAuthorizedBcId;
            LiveGmvMaxAvailable = newEntity.LiveGmvMaxAvailable;
            ProductGmvMaxAvailable = newEntity.ProductGmvMaxAvailable;
            IsRunningCustomShopAds = newEntity.IsRunningCustomShopAds;
            UnavailableReason = newEntity.UnavailableReason;
            SyncedAt = newEntity.SyncedAt;
        }
    }
}
