using System;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service interface cho việc đồng bộ dữ liệu báo cáo chi tiết cấp creative GMV Max Product Campaign
    /// </summary>
    public interface IGmvMaxProductCreativeSyncService : ITransientDependency
    {
        /// <summary>
        /// Đồng bộ báo cáo chi tiết cấp creative GMV Max Product Campaign theo BC ID 
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate"><PERSON><PERSON>y bắt đầu (tùy chọn)</param>
        /// <param name="endDate"><PERSON><PERSON><PERSON> kết thúc (tùy chọn)</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<GmvMaxProductCreativeSyncResult> SyncGmvMaxProductCreativeAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null);

        /// <summary>
        /// Đồng bộ báo cáo chi tiết cấp creative GMV Max Product Campaign cho tất cả Business Centers với khoảng thời gian mặc định
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<GmvMaxProductCreativeSyncResult> SyncAllGmvMaxProductCreativeForAllBcsAsync();
    }

    /// <summary>
    /// Kết quả đồng bộ dữ liệu báo cáo chi tiết cấp creative GMV Max Product Campaign
    /// </summary>
    public class GmvMaxProductCreativeSyncResult : SyncResultBase
    {
        /// <summary>
        /// Số BC đã đồng bộ
        /// </summary>
        public int BcCount { get; set; }

        /// <summary>
        /// Số Campaign đã đồng bộ
        /// </summary>
        public int CampaignCount { get; set; }

        /// <summary>
        /// Số ngày đã đồng bộ
        /// </summary>
        public int DayCount { get; set; }

        /// <summary>
        /// Số Store đã đồng bộ
        /// </summary>
        public int StoreCount { get; set; }

        /// <summary>
        /// Số sản phẩm đã đồng bộ
        /// </summary>
        public int ProductCount { get; set; }

        /// <summary>
        /// Số creative đã đồng bộ
        /// </summary>
        public int CreativeCount { get; set; }

        /// <summary>
        /// Số bản ghi đã bỏ qua do tất cả metrics đều là 0
        /// </summary>
        public int SkippedRecords { get; set; }

        public override int TotalSynced => UpdatedRecords + NewRecords + ErrorRecords;
    }
} 