﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TikTok.Migrations
{
    /// <inheritdoc />
    public partial class AddPropertiesProductCampaignDetail : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "Cost",
                table: "Raw_RawGmvMaxProductDetailProductReports",
                type: "decimal(15,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "CostPerOrder",
                table: "Raw_RawGmvMaxProductDetailProductReports",
                type: "decimal(15,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "ROI",
                table: "Raw_RawGmvMaxProductDetailProductReports",
                type: "decimal(15,2)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Cost",
                table: "Raw_RawGmvMaxProductDetailProductReports");

            migrationBuilder.DropColumn(
                name: "CostPerOrder",
                table: "Raw_RawGmvMaxProductDetailProductReports");

            migrationBuilder.DropColumn(
                name: "ROI",
                table: "Raw_RawGmvMaxProductDetailProductReports");
        }
    }
}
