using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TikTok.Entities;
using Volo.Abp.Domain.Repositories;

namespace TikTok.Repositories
{
    /// <summary>
    /// Repository interface cho GMV Max Identities
    /// </summary>
    public interface IRawGmvMaxIdentitiesRepository : IRepository<RawGmvMaxIdentitiesEntity, Guid>
    {
        /// <summary>
        /// L<PERSON>y danh sách identities theo Advertiser ID
        /// </summary>
        /// <param name="advertiserId">ID của advertiser</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách identities</returns>
        Task<List<RawGmvMaxIdentitiesEntity>> GetByAdvertiserIdAsync(
            string advertiserId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// L<PERSON>y danh sách identities theo Business Center ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách identities</returns>
        Task<List<RawGmvMaxIdentitiesEntity>> GetByBcIdAsync(
            string bcId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách identities theo nhiều Advertiser IDs
        /// </summary>
        /// <param name="advertiserIds">Danh sách Advertiser IDs</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách identities</returns>
        Task<List<RawGmvMaxIdentitiesEntity>> GetByAdvertiserIdsAsync(
            List<string> advertiserIds,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách identities theo Identity Type
        /// </summary>
        /// <param name="identityType">Loại identity</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách identities</returns>
        Task<List<RawGmvMaxIdentitiesEntity>> GetByIdentityTypeAsync(
            string identityType,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách identities khả dụng cho Product GMV Max
        /// </summary>
        /// <param name="bcId">ID của Business Center (optional)</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách identities khả dụng</returns>
        Task<List<RawGmvMaxIdentitiesEntity>> GetAvailableForProductGmvMaxAsync(
            string? bcId = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách identities khả dụng cho Live GMV Max
        /// </summary>
        /// <param name="bcId">ID của Business Center (optional)</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách identities khả dụng</returns>
        Task<List<RawGmvMaxIdentitiesEntity>> GetAvailableForLiveGmvMaxAsync(
            string? bcId = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Kiểm tra xem identity đã tồn tại chưa
        /// </summary>
        /// <param name="advertiserId">ID của advertiser</param>
        /// <param name="identityId">ID của identity</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>True nếu đã tồn tại</returns>
        Task<bool> ExistsAsync(
            string advertiserId,
            string identityId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy identity theo Advertiser ID và Identity ID
        /// </summary>
        /// <param name="advertiserId">ID của advertiser</param>
        /// <param name="identityId">ID của identity</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Identity entity hoặc null</returns>
        Task<RawGmvMaxIdentitiesEntity?> GetByAdvertiserAndIdentityIdAsync(
            string advertiserId,
            string identityId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Xóa tất cả identities của một advertiser
        /// </summary>
        /// <param name="advertiserId">ID của advertiser</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Task</returns>
        Task DeleteByAdvertiserIdAsync(
            string advertiserId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Xóa tất cả identities của một Business Center
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Task</returns>
        Task DeleteByBcIdAsync(
            string bcId,
            CancellationToken cancellationToken = default);
    }
}
