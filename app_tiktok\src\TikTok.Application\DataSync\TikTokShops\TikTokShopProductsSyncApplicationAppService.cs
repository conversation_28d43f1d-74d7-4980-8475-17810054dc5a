using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// Application service cho TikTok Shop Products Sync
    /// </summary>
    public class TikTokShopProductsSyncApplicationAppService : ApplicationService
    {
        private readonly ITikTokShopProductsSyncService _tikTokShopProductsSyncService;
        private readonly ILogger<TikTokShopProductsSyncApplicationAppService> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public TikTokShopProductsSyncApplicationAppService(
            ITikTokShopProductsSyncService tikTokShopProductsSyncService,
            ILogger<TikTokShopProductsSyncApplicationAppService> logger)
        {
            _tikTokShopProductsSyncService = tikTokShopProductsSyncService;
            _logger = logger;
        }

        /// <summary>
        /// Đồng bộ TikTok Shop Products theo Business Center ID
        /// Lấy advertiserId và storeId từ bảng RawGmvMaxIdentitiesEntity
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<TikTokShopProductsSyncResult> SyncTikTokShopProductsAsync(string bcId)
        {
            _logger.LogInformation("Bắt đầu đồng bộ TikTok Shop Products cho BC: {BcId}", bcId);

            try
            {
                var result = await _tikTokShopProductsSyncService.SyncTikTokShopProductsAsync(bcId);

                _logger.LogInformation("Hoàn thành đồng bộ TikTok Shop Products cho BC: {BcId}. " +
                    "Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, Lỗi: {Error}, " +
                    "Advertiser: {AdvertiserCount}, Store: {StoreCount}, Thời gian: {Duration}s",
                    bcId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.ErrorRecords,
                    result.AdvertiserCount, result.StoreCount, result.DurationSeconds);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi đồng bộ TikTok Shop Products cho BC: {BcId}", bcId);
                throw;
            }
        }

        /// <summary>
        /// Đồng bộ TikTok Shop Products theo Store ID (được giữ lại cho tương thích)
        /// </summary>
        /// <param name="storeId">ID của Store</param>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<TikTokShopProductsSyncResult> SyncTikTokShopProductsByIdentityAsync(string storeId, string advertiserId, string bcId)
        {
            _logger.LogInformation("Bắt đầu đồng bộ TikTok Shop Products cho Store: {StoreId}, Advertiser: {AdvertiserId}, BC: {BcId}",
                storeId, advertiserId, bcId);

            try
            {
                var result = await _tikTokShopProductsSyncService.SyncTikTokShopProductsByIdentityAsync(storeId, advertiserId, bcId);

                _logger.LogInformation("Hoàn thành đồng bộ TikTok Shop Products cho Store: {StoreId}. " +
                    "Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, Lỗi: {Error}, Thời gian: {Duration}s",
                    storeId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.ErrorRecords, result.DurationSeconds);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi đồng bộ TikTok Shop Products cho Store: {StoreId}", storeId);
                throw;
            }
        }

        /// <summary>
        /// Đồng bộ TikTok Shop Products cho nhiều Store của một Advertiser
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="storeIds">Danh sách Store IDs (null để đồng bộ tất cả)</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<TikTokShopProductsSyncResult> SyncManyTikTokShopProductsAsync(string advertiserId, string bcId, List<string>? storeIds = null)
        {
            _logger.LogInformation("Bắt đầu đồng bộ nhiều TikTok Shop Products cho Advertiser: {AdvertiserId}, BC: {BcId}, Store Count: {StoreCount}",
                advertiserId, bcId, storeIds?.Count ?? 0);

            try
            {
                var result = await _tikTokShopProductsSyncService.SyncManyTikTokShopProductsAsync(advertiserId, bcId, storeIds);

                _logger.LogInformation("Hoàn thành đồng bộ nhiều TikTok Shop Products cho Advertiser: {AdvertiserId}. " +
                    "Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, Lỗi: {Error}, Store: {StoreCount}, Thời gian: {Duration}s",
                    advertiserId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.ErrorRecords,
                    result.StoreCount, result.DurationSeconds);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi đồng bộ nhiều TikTok Shop Products cho Advertiser: {AdvertiserId}", advertiserId);
                throw;
            }
        }

        /// <summary>
        /// Đồng bộ tất cả TikTok Shop Products cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<TikTokShopProductsSyncResult> SyncAllTikTokShopProductsAsync()
        {
            _logger.LogInformation("Bắt đầu đồng bộ tất cả TikTok Shop Products cho tất cả BC");

            try
            {
                var result = await _tikTokShopProductsSyncService.SyncAllTikTokShopProductsAsync();

                _logger.LogInformation("Hoàn thành đồng bộ tất cả TikTok Shop Products. " +
                    "Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, Lỗi: {Error}, " +
                    "BC: {BcCount}, Advertiser: {AdvertiserCount}, Store: {StoreCount}, Thời gian: {Duration}s",
                    result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.ErrorRecords,
                    result.BcCount, result.AdvertiserCount, result.StoreCount, result.DurationSeconds);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi đồng bộ tất cả TikTok Shop Products");
                throw;
            }
        }
    }
}
