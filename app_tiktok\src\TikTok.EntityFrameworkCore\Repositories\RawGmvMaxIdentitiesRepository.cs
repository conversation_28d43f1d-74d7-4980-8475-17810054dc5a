using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using TikTok.Entities;
using TikTok.EntityFrameworkCore;
using TikTok.Repositories;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace TikTok.EntityFrameworkCore.Repositories
{
    /// <summary>
    /// Repository implementation cho GMV Max Identities
    /// </summary>
    public class RawGmvMaxIdentitiesRepository : EfCoreRepository<TikTokDbContext, RawGmvMaxIdentitiesEntity, Guid>, IRawGmvMaxIdentitiesRepository
    {
        public RawGmvMaxIdentitiesRepository(IDbContextProvider<TikTokDbContext> dbContextProvider) : base(dbContextProvider)
        {
        }

        public async Task<List<RawGmvMaxIdentitiesEntity>> GetByAdvertiserIdAsync(
            string advertiserId,
            CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => x.AdvertiserId == advertiserId)
                .OrderBy(x => x.IdentityType)
                .ThenBy(x => x.DisplayName ?? x.UserName ?? x.IdentityId)
                .ToListAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<List<RawGmvMaxIdentitiesEntity>> GetByBcIdAsync(
            string bcId,
            CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => x.BcId == bcId)
                .OrderBy(x => x.AdvertiserId)
                .ThenBy(x => x.IdentityType)
                .ThenBy(x => x.DisplayName ?? x.UserName ?? x.IdentityId)
                .ToListAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<List<RawGmvMaxIdentitiesEntity>> GetByAdvertiserIdsAsync(
            List<string> advertiserIds,
            CancellationToken cancellationToken = default)
        {
            if (advertiserIds == null || !advertiserIds.Any())
            {
                return new List<RawGmvMaxIdentitiesEntity>();
            }

            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => advertiserIds.Contains(x.AdvertiserId))
                .OrderBy(x => x.AdvertiserId)
                .ThenBy(x => x.IdentityType)
                .ThenBy(x => x.DisplayName ?? x.UserName ?? x.IdentityId)
                .ToListAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<List<RawGmvMaxIdentitiesEntity>> GetByIdentityTypeAsync(
            string identityType,
            CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => x.IdentityType == identityType)
                .OrderBy(x => x.BcId)
                .ThenBy(x => x.AdvertiserId)
                .ThenBy(x => x.DisplayName ?? x.UserName ?? x.IdentityId)
                .ToListAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<List<RawGmvMaxIdentitiesEntity>> GetAvailableForProductGmvMaxAsync(
            string? bcId = null,
            CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            var query = dbSet.Where(x => x.ProductGmvMaxAvailable == true);

            if (!string.IsNullOrEmpty(bcId))
            {
                query = query.Where(x => x.BcId == bcId);
            }

            return await query
                .OrderBy(x => x.BcId)
                .ThenBy(x => x.AdvertiserId)
                .ThenBy(x => x.DisplayName ?? x.UserName ?? x.IdentityId)
                .ToListAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<List<RawGmvMaxIdentitiesEntity>> GetAvailableForLiveGmvMaxAsync(
            string? bcId = null,
            CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            var query = dbSet.Where(x => x.LiveGmvMaxAvailable == true);

            if (!string.IsNullOrEmpty(bcId))
            {
                query = query.Where(x => x.BcId == bcId);
            }

            return await query
                .OrderBy(x => x.BcId)
                .ThenBy(x => x.AdvertiserId)
                .ThenBy(x => x.DisplayName ?? x.UserName ?? x.IdentityId)
                .ToListAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<bool> ExistsAsync(
            string advertiserId,
            string identityId,
            CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .AnyAsync(x => x.AdvertiserId == advertiserId && x.IdentityId == identityId,
                    GetCancellationToken(cancellationToken));
        }

        public async Task<RawGmvMaxIdentitiesEntity?> GetByAdvertiserAndIdentityIdAsync(
            string advertiserId,
            string identityId,
            CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .FirstOrDefaultAsync(x => x.AdvertiserId == advertiserId && x.IdentityId == identityId,
                    GetCancellationToken(cancellationToken));
        }

        public async Task DeleteByAdvertiserIdAsync(
            string advertiserId,
            CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            var entities = await dbSet
                .Where(x => x.AdvertiserId == advertiserId)
                .ToListAsync(GetCancellationToken(cancellationToken));

            if (entities.Any())
            {
                dbSet.RemoveRange(entities);
            }
        }

        public async Task DeleteByBcIdAsync(
            string bcId,
            CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            var entities = await dbSet
                .Where(x => x.BcId == bcId)
                .ToListAsync(GetCancellationToken(cancellationToken));

            if (entities.Any())
            {
                dbSet.RemoveRange(entities);
            }
        }

        public async Task<RawGmvMaxIdentitiesEntity?> GetByCompositeKeyAsync(
            string advertiserId,
            string? storeId,
            string? storeAuthorizedBcId,
            string identityId,
            CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .FirstOrDefaultAsync(x =>
                    x.AdvertiserId == advertiserId &&
                    x.StoreId == storeId &&
                    x.StoreAuthorizedBcId == storeAuthorizedBcId &&
                    x.IdentityId == identityId,
                    GetCancellationToken(cancellationToken));
        }

        public async Task<List<RawGmvMaxIdentitiesEntity>> GetByCompositeKeysAsync(
            string advertiserId,
            List<string> identityIds,
            CancellationToken cancellationToken = default)
        {
            if (identityIds == null || !identityIds.Any())
            {
                return new List<RawGmvMaxIdentitiesEntity>();
            }

            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => x.AdvertiserId == advertiserId && identityIds.Contains(x.IdentityId))
                .ToListAsync(GetCancellationToken(cancellationToken));
        }
    }
}
