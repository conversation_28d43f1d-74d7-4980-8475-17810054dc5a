/**
 * ✅ COPY HOÀN TOÀN TiktokGmvMaxCampaignPivotTable từ factGmvMaxCampaign.js
 * Chỉ thay đổi container ID và localStorage keys để tránh conflict
 */
class TiktokGmvMaxCampaignPivotTableForGmvMax {
    constructor(containerId) {
        this.containerId = containerId;
        this.pivotTableObj = null;
        this.currencyManager = new Currency('campaign');
        this.currentCurrency = this.currencyManager.getCurrentCurrency();
        this.availableCurrencies = ['USD', 'VND'];

        // Use shared batched refresh
        this.pendingRefresh = false;
        this.refreshTimeout = null;

        // Shared cached lookups/hash
        this.cachedLookups = null;
        this.lastDataHash = null;

        this.performanceMetrics = {
            refreshCount: 0,
            dataProcessingTime: 0,
            lastRefreshTime: null,
            totalProcessedRecords: 0,
        };

        this.dataBoundTimeout = null;

        // Get alert thresholds from window.CampaignAlertThresholds
        this.alertThresholds = window.CampaignAlertThresholds || {
            roasCritical: 1.5, // ROI < 1.5
            roasLow: 2.0, // ROI < 2.0
            roasGood: 3.0, // ROI > 3.0
            tacosHigh: 30, // TACOS > 30%
            tacosMedium: 20, // TACOS > 20%
            budgetUtilizationHigh: 80, // Budget > 80%
            budgetUtilizationMedium: 60, // Budget > 60%
        };

        this.heatmapThresholds = {
            // ROI thresholds
            roasEmergency: 1.0, // Dark red - campaign losing money
            roasCritical: this.alertThresholds.roasCritical, // Red - critical ROI
            roasLow: this.alertThresholds.roasLow, // Orange - low ROI
            roasGood: this.alertThresholds.roasGood, // Green - good ROI
            roasExcellent: 5.0, // Dark green - excellent ROI
            // Colors for heatmap visualization
            colors: {
                emergency: { bg: '#d32f2f', color: '#ffffff' }, // Dark red
                critical: { bg: '#f44336', color: '#ffffff' }, // Red
                low: { bg: '#ff9800', color: '#ffffff' }, // Orange
                warning: { bg: '#ffc107', color: '#212121' }, // Yellow
                good: { bg: '#4caf50', color: '#ffffff' }, // Green
                excellent: { bg: '#2e7d32', color: '#ffffff' }, // Dark green
                veryHigh: { bg: '#1b5e20', color: '#ffffff' }, // Very dark green
            },
        };
    }

    // ✅ COPY HOÀN TOÀN method initial() từ factGmvMaxCampaign.js
    async initial() {
        try {
            const dataSource = await this.extractPivotDataOptimized([
                'campaign',
            ]);

            this.fullDataSource = dataSource;

            // Get filtered values based on permissions
            const filteredValues = await this.getCurrencyValues(
                this.getStoredColumnAggregations()
            );

            this.pivotTableObj = new ej.pivotview.PivotView({
                dataSourceSettings: {
                    dataSource: dataSource,
                    allowLabelFilter: false,
                    allowValueFilter: false,
                    allowMemberFilter: false,
                    enableSorting: true,
                    allowCalculatedField: true,
                    // ✅ PERFORMANCE: Enable virtual scrolling for large datasets
                    enableVirtualScrolling: true,

                    rows: [
                        {
                            name: 'BusinessCenterName',
                            caption: 'Trung tâm kinh doanh',
                            showSubTotals: true,
                        },
                        {
                            name: 'StoreName',
                            caption: 'Tên Shop',
                            showSubTotals: true,
                        },
                        {
                            name: 'CampaignName',
                            caption: 'Tên chiến dịch',
                            showSubTotals: false,
                        },
                    ],

                    columns: this.getSmartTimeColumns(),

                    values: filteredValues,

                    filters: [],

                    formatSettings: this.getCurrencyFormatSettings(),

                    calculatedFieldSettings: [
                        {
                            name: 'Revenue_Per_Click',
                            formula:
                                '"Clicks" > 0 ? ("GrossRevenue" / "Clicks") : 0',
                            caption: 'Doanh thu mỗi click',
                        },
                        {
                            name: 'Campaign_Efficiency_Score',
                            formula:
                                '("ROI" > 3) ? 100 : (("ROI" > 2) ? 70 : (("ROI" > 1.5) ? 40 : 20))',
                            caption: 'Điểm hiệu quả chiến dịch',
                        },
                    ],

                    expandAll: false, // ✅ Collapse rows by default for better performance

                    excludeFields: [
                        'NetCost',
                        'NetCostVND', // Legacy VND fields
                        'CostPerOrderVND', // Legacy VND fields
                        'CPMVND', // Legacy VND fields
                        'CPCVND', // Legacy VND fields
                        // Note: Keep USD fields available for calculations
                    ],

                    sortSettings: [
                        { name: 'ROAS', order: 'Descending' },
                        {
                            name:
                                this.currentCurrency === 'VND'
                                    ? 'GrossRevenueVND'
                                    : this.currentCurrency === 'USD'
                                    ? 'GrossRevenueUSD'
                                    : 'GrossRevenue',
                            order: 'Descending',
                        },
                    ],
                },

                locale: 'vi-VN',
                height: 1000,
                width: '100%',
                showGroupingBar: false,
                showFieldList: true,
                allowExcelExport: true,
                allowPdfExport: false,
                showToolbar: true,
                // ✅ PERFORMANCE: Enable lazy loading and virtual scrolling for better performance
                enableVirtualScrolling: true,
                enableLazyLoading: true,

                // ✅ PERFORMANCE: Virtual scrolling configuration for large datasets
                virtualScrollingSettings: {
                    enableVirtualScrolling: true,
                    allowVirtualScrolling: true,
                    virtualScrollMode: 'Both', // Both rows and columns
                    rowHeight: 30, // Fixed row height for better performance
                    columnWidth: 140, // Fixed column width
                },

                showValuesButton: false,
                showRowSubTotals: false,
                showColumnSubTotals: false,
                showGrandTotals: false,
                gridSettings: {
                    layout: 'Tabular',
                    columnWidth: 140,
                    allowSelection: false,
                    selectionSettings: { mode: 'Cell', type: 'Multiple' },
                    // ✅ PERFORMANCE: Optimized grid settings for large datasets
                    enableVirtualScrolling: true,
                    allowVirtualScrolling: true,
                    rowHeight: 30,
                    enableAutoFit: false, // Disable auto-fit for better performance
                    allowResizing: false, // Disable resizing for better performance
                },

                toolbar: [
                    'SubTotal',
                    'GrandTotal',
                    'FieldList',
                    'ConditionalFormatting',
                    'NumberFormatting',
                ],

                conditionalFormatSettings:
                    this.generateCampaignHeatmapFormatting(),
                allowConditionalFormatting: true,

                cellClick: (args) => {
                    this.handleCellClick(args);
                },

                dataBound: () => {
                    if (this.dataBoundTimeout) {
                        clearTimeout(this.dataBoundTimeout);
                    }
                    this.dataBoundTimeout = setTimeout(() => {
                        this.updateCampaignInsights();
                    }, 200); // 200ms debounce
                },

                // Chart settings for campaign analysis
                chartSettings: {
                    chartSeries: {
                        type: 'Column',
                        animation: { enable: true },
                    },
                    primaryYAxis: {
                        title: `Doanh thu (${this.currentCurrency})`,
                        labelFormat: 'C0',
                    },
                },
            });

            // Render to container
            this.pivotTableObj.appendTo(`#${this.containerId}`);

            // ✅ PERFORMANCE: Optimized refresh for virtual scrolling
            setTimeout(() => {
                if (this.pivotTableObj && this.pivotTableObj.refresh) {
                    this.pivotTableObj.refresh();
                }
            }, 200); // Increased delay for virtual scrolling initialization

            await this.applyInitialValuesConfiguration();
        } catch (error) {
            console.error('❌ Error in Live Campaign Pivot initial():', error);
            throw error;
        }
    }

    // ✅ COPY HOÀN TOÀN từ factGmvMaxCampaign.js
    async applyInitialValuesConfiguration() {
        // Get saved configuration from localStorage - shared with FactGmvMaxCampaign
        let savedConfig = localStorage.getItem('campaignColumnAggregations');
        let desiredValues;

        if (savedConfig) {
            try {
                const config = JSON.parse(savedConfig);
                // Support new format with selectedFields and aggregations
                if (config.selectedFields && config.aggregations) {
                    desiredValues = config.selectedFields;
                } else {
                    // Old format - treat as aggregations only, use defaults
                    desiredValues = [
                        'GrossRevenue',
                        'Cost',
                        'ROAS',
                        'Orders',
                        'CostPerOrder',
                    ];
                }
            } catch (e) {
                console.warn(
                    '⚠️ Error parsing saved configuration, using defaults:',
                    e
                );
                desiredValues = [
                    'GrossRevenue',
                    'Cost',
                    'ROAS',
                    'Orders',
                    'CostPerOrder',
                ];
            }
        } else {
            desiredValues = [
                'GrossRevenue',
                'Cost',
                'ROAS',
                'Orders',
                'CostPerOrder',
            ];
        }

        // Apply values directly to pivot table configuration without triggering refresh
        if (desiredValues && desiredValues.length > 0) {
            const currentAggregations = this.getStoredColumnAggregations();
            const newValues = await this.getCurrencyValues(currentAggregations);

            const filteredValues = newValues.filter((value) =>
                desiredValues.includes(value.name)
            );

            this.pivotTableObj.dataSourceSettings.values = filteredValues;
        }
    }

    // ✅ Copy batchedRefresh method from factGmvMaxCampaign.js
    batchedRefresh(changes = {}) {
        if (
            !this._batchedRefresh &&
            window.dataProcessingUtils &&
            window.dataProcessingUtils.createBatchedRefresh
        ) {
            this._batchedRefresh =
                window.dataProcessingUtils.createBatchedRefresh(
                    this.pivotTableObj,
                    this.performanceMetrics
                );
        }
        // If pivot not ready yet, fallback to local minimal apply
        if (!this._batchedRefresh) {
            if (changes.values && this.pivotTableObj)
                this.pivotTableObj.dataSourceSettings.values = changes.values;
            if (changes.dataSource && this.pivotTableObj)
                this.pivotTableObj.dataSourceSettings.dataSource =
                    changes.dataSource;
            if (changes.formatSettings && this.pivotTableObj)
                this.pivotTableObj.dataSourceSettings.formatSettings =
                    changes.formatSettings;
            if (changes.conditionalFormatSettings && this.pivotTableObj)
                this.pivotTableObj.dataSourceSettings.conditionalFormatSettings =
                    changes.conditionalFormatSettings;
            if (changes.filterSettings && this.pivotTableObj)
                this.pivotTableObj.dataSourceSettings.filterSettings =
                    changes.filterSettings;
            this.pivotTableObj &&
                this.pivotTableObj.refresh &&
                this.pivotTableObj.refresh();
            return;
        }
        this._batchedRefresh(changes);
    }

    // ✅ COPY HOÀN TOÀN extractPivotDataOptimized từ factGmvMaxCampaign.js
    async extractPivotDataOptimized(includes = ['campaign'], dataToUse = null) {
        let sourceData = dataToUse;

        if (!sourceData) {
            // ✅ Sử dụng data từ global scope như màn hình hiện có
            sourceData = window.initialGmvMaxCampaignDataForGmvMax;
        }

        if (!sourceData?.factGmvMaxCampaigns) {
            return [];
        }

        const facts = sourceData.factGmvMaxCampaigns;
        if (!facts || facts.length === 0) {
            return [];
        }

        const transformedData = [];

        // ✅ PERFORMANCE: Increased batch size for better performance with large datasets
        const BATCH_SIZE = 2000;

        const lookups = this.createCachedLookups(sourceData);

        const processingStart = performance.now();

        for (let i = 0; i < facts.length; i += BATCH_SIZE) {
            const batch = facts.slice(i, i + BATCH_SIZE);

            const batchResults = batch.map((fact) => {
                const dateLookup = (lookups && lookups.date) || {};
                const adAccountLookup = (lookups && lookups.adAccount) || {};
                const businessCenterLookup =
                    (lookups && lookups.businessCenter) || {};
                const storeLookup = (lookups && lookups.store) || {};
                const productLookup = (lookups && lookups.product) || {};

                const dateInfo = dateLookup[fact.dimDateId] || {};
                const adAccountInfo =
                    adAccountLookup[fact.dimAdAccountId] || {};
                const businessCenterInfo =
                    businessCenterLookup[fact.dimBusinessCenterId] || {};
                const storeInfo = storeLookup[fact.dimStoreId] || {};
                const productInfo = fact.dimProductId
                    ? productLookup[fact.dimProductId]
                    : null;

                const transformedRecord = {
                    // Core campaign identification
                    CampaignId: fact.campaignId,
                    CampaignName: fact.campaignName || fact.campaignId,
                    ShoppingAdsType: fact.shoppingAdsType || 'UNKNOWN',
                    OperationStatus: fact.operationStatus || 'UNKNOWN',

                    // Business context
                    BusinessCenterName:
                        businessCenterInfo.bcName || 'Unknown BC',
                    BusinessCenterId: businessCenterInfo.bcId || '',
                    AdAccountName: adAccountInfo.advertiserName || '',
                    AdAccountId: adAccountInfo.advertiserId || '',

                    // Store and Product info
                    StoreName: storeInfo.storeName || 'Unknown Store',
                    StoreId: fact.storeId,
                    ProductName: productInfo?.productName || '',
                    ProductId: fact.productId || '',

                    // Date context
                    Date: dateInfo.fullDate || fact.date,
                    DateFormatted:
                        dateInfo.dateFormat_DDMMYYYY || formatDate(fact.date),
                    DateKey: fact.dimDateId,
                    Year: dateInfo.year || new Date(fact.date).getFullYear(),
                    Month: dateInfo.month || new Date(fact.date).getMonth() + 1,
                    MonthName: dateInfo.monthName || getMonthName(fact.date),
                    WeekDay: getVietnameseWeekday(new Date(fact.date)),
                    WeekOfYear: getWeekOfYear(fact.date),
                    WeekOfMonth: getWeekOfMonth(fact.date),
                    WeekStartDate: getWeekStartDate(fact.date),
                    WeekEndDate: getWeekEndDate(fact.date),
                    Quarter: getQuarter(fact.date),

                    // ✅ New formatted fields for smart grouping
                    WeekMonthYear: getWeekMonthYear(fact.date),
                    MonthYear: getMonthYear(fact.date),
                    YearFormatted: getYearFormatted(fact.date),

                    // Financial metrics with currency support
                    Cost: this.getCurrencyValue(fact, 'cost'),
                    NetCost: this.getCurrencyValue(fact, 'netCost'),
                    GrossRevenue: this.getCurrencyValue(fact, 'grossRevenue'),
                    AdsRevenue: this.getCurrencyValue(fact, 'adsRevenue'),
                    OrganicRevenue: this.getCurrencyValue(
                        fact,
                        'organicRevenue'
                    ),
                    Budget: this.getCurrencyValue(fact, 'budget'),

                    // Performance metrics
                    Orders: fact.orders || 0,
                    CostPerOrder: this.getCurrencyValue(fact, 'costPerOrder'),
                    ROAS: fact.roas || 0, // ✅ Fixed: Map to ROAS field name
                    // ✅ DEBUG: Log ROAS data for first record
                    ...((i === 0 &&
                        console.log('🔍 ROAS Debug - First record:', {
                            'fact.roas': fact.roas,
                            'ROAS mapped': fact.roas || 0,
                        })) ||
                        {}),
                    TACOS: fact.tacos || 0,

                    // Campaign-level metrics for new entity structure
                    BidType: fact.bidType || '',
                    RoasBid: fact.roasBid || 0,
                    TargetRoiBudget: this.getCurrencyValue(
                        fact,
                        'targetRoiBudget'
                    ),
                    MaxDeliveryBudget: this.getCurrencyValue(
                        fact,
                        'maxDeliveryBudget'
                    ),
                    ScheduleType: fact.scheduleType || '',

                    // LIVE campaign specific fields
                    TtAccountName: fact.ttAccountName || '',
                    LiveViews: fact.liveViews || 0,
                    CostPerLiveView: this.getCurrencyValue(
                        fact,
                        'costPerLiveView'
                    ),
                    TenSecondLiveViews: fact.tenSecondLiveViews || 0,
                    LiveFollows: fact.liveFollows || 0,

                    Currency: fact.currency || 'USD',
                };

                this.calculateCampaignMetricsInline(transformedRecord);
                this.addCampaignClassificationsInline(transformedRecord);

                return transformedRecord;
            });

            transformedData.push(...batchResults);

            if (i + BATCH_SIZE < facts.length) {
                await new Promise((resolve) => setTimeout(resolve, 0));
            }
        }

        // ✅ PERFORMANCE: Store processing metrics
        const processingDuration = performance.now() - processingStart;
        this.performanceMetrics.dataProcessingTime = processingDuration;
        this.performanceMetrics.totalProcessedRecords = transformedData.length;

        return transformedData;
    }

    // ✅ Inline optimized metrics calculation
    calculateCampaignMetricsInline(record) {
        // ✅ Single-pass calculation for better performance
        const clicks = record.Clicks || 0;
        const impressions = record.Impressions || 0;
        const grossRevenue = record.GrossRevenue || 0;
        const cost = record.Cost || 0;
        const orders = record.Orders || 0;

        // Conversion rate
        record.ConversionRate =
            clicks > 0 ? parseFloat(((orders / clicks) * 100).toFixed(2)) : 0;

        // Revenue per impression
        record.RevenuePerImpression =
            impressions > 0
                ? parseFloat((grossRevenue / impressions).toFixed(4))
                : 0;

        // Profit margin
        record.ProfitMargin =
            grossRevenue > 0
                ? parseFloat(
                      (((grossRevenue - cost) / grossRevenue) * 100).toFixed(2)
                  )
                : 0;
    }

    // ✅ Inline optimized classifications
    addCampaignClassificationsInline(record) {
        const roas = record.ROAS || 0; // ✅ Fixed: Use ROAS instead of ROI
        const tacosPct = (record.TACOS || 0) * 100;

        // ✅ Inline calculations for better performance
        record.ROASStatus = // ✅ Changed from ROIStatus to ROASStatus
            roas >= this.alertThresholds.roasGood
                ? 'Excellent'
                : roas >= this.alertThresholds.roasLow
                ? 'Good'
                : roas >= this.alertThresholds.roasCritical
                ? 'Warning'
                : 'Critical';

        record.TACOSStatus =
            tacosPct >= this.alertThresholds.tacosHigh
                ? 'High'
                : tacosPct >= this.alertThresholds.tacosMedium
                ? 'Medium'
                : 'Low';

        record.CampaignHealth =
            roas >= 3.0 && tacosPct <= 20
                ? 'Excellent'
                : roas >= 2.0 && tacosPct <= 30
                ? 'Good'
                : roas >= 1.5
                ? 'Fair'
                : 'Poor';

        record.AlertLevel =
            roas < 1.5 || tacosPct > this.alertThresholds.tacosHigh
                ? 'High'
                : roas < 2.0 || tacosPct > this.alertThresholds.tacosMedium
                ? 'Medium'
                : 'Low';
    }

    // ✅ COPY tất cả helper methods từ factGmvMaxCampaign.js
    generateDataHash(data) {
        if (!data) return null;

        const factCount = data.factGmvMaxCampaigns?.length || 0;
        const dimCounts = [
            (data.dimAdAccounts || data.DimAdAccounts)?.length || 0,
            (data.dimBusinessCenters || data.DimBusinessCenters)?.length || 0,
            (data.dimStores || data.DimStores)?.length || 0,
            (data.dimProducts || data.DimProducts)?.length || 0,
            (data.dimDates || data.DimDates)?.length || 0,
        ].join('-');

        return `${factCount}_${dimCounts}`;
    }

    createCachedLookups(data) {
        if (!data) return {};

        const dataHash = this.generateDataHash(data);
        if (this.lastDataHash === dataHash && this.cachedLookups) {
            return this.cachedLookups;
        }

        const adAccountsRaw = data.dimAdAccounts || data.DimAdAccounts || [];
        const businessCentersRaw =
            data.dimBusinessCenters || data.DimBusinessCenters || [];
        const storesRaw = data.dimStores || data.DimStores || [];
        const productsRaw = data.dimProducts || data.DimProducts || [];
        const datesRaw = data.dimDates || data.DimDates || [];

        const normalizeId = (x) => x?.id ?? x?.Id;

        const adAccounts = adAccountsRaw.map((a) => ({
            id: normalizeId(a),
            advertiserName:
                a?.advertiserName ?? a?.AdvertiserName ?? a?.name ?? a?.Name,
            advertiserId: a?.advertiserId ?? a?.AdvertiserId ?? normalizeId(a),
        }));

        const businessCenters = businessCentersRaw.map((b) => ({
            id: normalizeId(b),
            bcName: b?.bcName ?? b?.BcName ?? b?.name ?? b?.Name,
            bcId: b?.bcId ?? b?.BcId ?? normalizeId(b),
        }));

        const stores = storesRaw.map((s) => ({
            id: normalizeId(s),
            storeName: s?.storeName ?? s?.StoreName ?? s?.name ?? s?.Name,
            storeId: s?.storeId ?? s?.StoreId ?? normalizeId(s),
        }));

        const products = productsRaw.map((p) => ({
            id: normalizeId(p),
            productName: p?.productName ?? p?.ProductName ?? p?.name ?? p?.Name,
        }));

        const dates = datesRaw.map((d) => ({
            id: normalizeId(d),
            fullDate: d?.fullDate ?? d?.FullDate ?? d?.date ?? d?.Date,
            dateFormat_DDMMYYYY:
                d?.dateFormat_DDMMYYYY ?? d?.DateFormat_DDMMYYYY,
            year: d?.year ?? d?.Year,
            month: d?.month ?? d?.Month,
            monthName: d?.monthName ?? d?.MonthName,
        }));

        this.cachedLookups = {
            adAccount: this.createLookup(adAccounts, 'id'),
            businessCenter: this.createLookup(businessCenters, 'id'),
            store: this.createLookup(stores, 'id'),
            product: this.createLookup(products, 'id'),
            date: this.createLookup(dates, 'id'),
        };

        this.lastDataHash = dataHash;
        return this.cachedLookups;
    }

    // ✅ COPY tất cả các methods từ factGmvMaxCampaign.js
    createLookup(array, keyField) {
        if (!array) return {};
        const lookup = {};
        array.forEach((item) => {
            lookup[item[keyField]] = item;
        });
        return lookup;
    }

    getCurrencyValue(fact, fieldName) {
        const currency = this.currentCurrency;

        // ✅ CURRENCY FIELD SELECTION LOGIC
        // If currency is VND, use VND field; if USD, use USD field; otherwise use original field
        if (currency === 'VND') {
            const vndValue = fact[`${fieldName}VND`];
            if (vndValue !== undefined) {
                return vndValue || 0;
            }
        } else if (currency === 'USD') {
            const usdValue = fact[`${fieldName}USD`];
            if (usdValue !== undefined) {
                return usdValue || 0;
            }
        }

        // Fallback to original field
        const originalValue = fact[fieldName];
        if (originalValue !== undefined) {
            return originalValue || 0;
        }

        return 0;
    }

    getCurrencyFormatSettings() {
        const currency = this.currentCurrency;
        const currencySymbol = currency === 'VND' ? '₫' : '$';

        return [
            {
                name: 'Cost',
                format: 'N0',
                suffix: ` ${currencySymbol}`,
            },
            {
                name: 'GrossRevenue',
                format: 'N0',
                suffix: ` ${currencySymbol}`,
            },
            {
                name: 'CostPerOrder',
                format: 'N0',
                suffix: ` ${currencySymbol}`,
            },
            {
                name: 'ROAS',
                format: 'N2',
                suffix: 'x',
            },
            {
                name: 'TACOS',
                format: 'P1',
                // Percentage format handles the symbol; do not add suffix
            },
        ];
    }

    async getCurrencyValues(columnAggregations = null) {
        // ✅ NEW: Dynamic currency support with proper field selection
        const currency = this.currentCurrency;
        const currencySymbol = currency === 'VND' ? '₫' : '$';
        const aggregations =
            columnAggregations || this.getStoredColumnAggregations();

        // Get unified FactGmvMax permissions - wait for ABP to be ready
        await window.PermissionHelper.waitForABP();
        const permissions = window.PermissionHelper.getPermissions('campaign');

        // ✅ SIMPLIFIED: Use original fields first to test
        const allValues = [
            // Revenue Metrics
            {
                name: 'GrossRevenue',
                caption: `Tổng doanh thu (${currencySymbol})`,
                type: aggregations['GrossRevenue'] || 'Sum',
                showSubTotals: true,
                category: 'metrics',
            },

            // Cost Metrics
            {
                name: 'Cost',
                caption: `Chi phí quảng cáo (${currencySymbol})`,
                type: aggregations['Cost'] || 'Sum',
                showSubTotals: true,
                category: 'spending',
            },
            {
                name: 'CostPerOrder',
                caption: `Chi phí mỗi đơn (${currencySymbol})`,
                type: aggregations['CostPerOrder'] || 'Avg',
                showSubTotals: false,
                category: 'spending',
            },

            // Performance Metrics
            {
                name: 'ROAS',
                caption: 'ROI (Tỷ lệ hoàn vốn đầu tư)',
                type: aggregations['ROAS'] || 'Avg',
                showSubTotals: true,
                category: 'metrics',
            },
            {
                name: 'TACOS',
                caption: 'TACOS (Tỷ lệ chi phí quảng cáo trên doanh thu)',
                type: aggregations['TACOS'] || 'Avg',
                showSubTotals: true,
                category: 'restricted',
            },

            // Volume Metrics
            {
                name: 'Orders',
                caption: 'Số đơn hàng',
                type: aggregations['Orders'] || 'Sum',
                showSubTotals: true,
                category: 'metrics',
            },
        ];

        // Filter values based on permissions
        const filteredValues = allValues.filter((value) => {
            if (permissions.viewAll || permissions.viewAllAdvertisers)
                return true;
            if (
                value.category === 'spending' &&
                (permissions.viewSpending || permissions.viewAllAdvertisers)
            )
                return true;
            if (
                value.category === 'metrics' &&
                (permissions.viewMetrics || permissions.viewAllAdvertisers)
            )
                return true;
            if (
                value.category === 'restricted' &&
                (permissions.viewAll || permissions.viewAllAdvertisers)
            )
                return true;
            return false;
        });

        return filteredValues;
    }

    // ✅ NEW: Get stored column aggregations from localStorage (GIỐNG HỆT FACTBALANCE)
    getStoredColumnAggregations() {
        const stored = localStorage.getItem('campaignColumnAggregations');
        if (stored) {
            try {
                return JSON.parse(stored);
            } catch (e) {
                // Use defaults on parse error
            }
        }

        // Return default aggregations for campaigns
        return {
            Cost: 'Sum',
            GrossRevenue: 'Sum',
            Orders: 'Sum',
            ROAS: 'Avg',
            TACOS: 'Avg',
            CostPerOrder: 'Avg',
        };
    }

    // ✅ NEW: Store column aggregations to localStorage (GIỐNG HỆT FACTBALANCE)
    storeColumnAggregations(aggregations) {
        localStorage.setItem(
            'campaignColumnAggregations',
            JSON.stringify(aggregations)
        );
    }

    // ✅ COPY tất cả các methods còn lại từ factGmvMaxCampaign.js
    generateCampaignHeatmapFormatting() {
        const formatSettings = [];
        const measures = ['ROAS', 'TACOS'];
        const thresholds = this.heatmapThresholds;

        // ROAS formatting
        formatSettings.push(
            // Emergency ROAS (< 1.0) - Dark Red
            {
                measure: 'ROAS',
                value1: 0,
                value2: thresholds.roasEmergency,
                conditions: 'Between',
                style: {
                    backgroundColor: thresholds.colors.emergency.bg,
                    color: thresholds.colors.emergency.color,
                    fontWeight: 'bold',
                    border: '2px solid #b71c1c',
                },
            },
            // Critical ROAS (1.0 - 1.5) - Red
            {
                measure: 'ROAS',
                value1: thresholds.roasEmergency,
                value2: thresholds.roasCritical,
                conditions: 'Between',
                style: {
                    backgroundColor: thresholds.colors.critical.bg,
                    color: thresholds.colors.critical.color,
                    fontWeight: 'bold',
                },
            },
            // Low ROAS (1.5 - 2.0) - Orange
            {
                measure: 'ROAS',
                value1: thresholds.roasCritical,
                value2: thresholds.roasLow,
                conditions: 'Between',
                style: {
                    backgroundColor: thresholds.colors.low.bg,
                    color: thresholds.colors.low.color,
                    fontWeight: '500',
                },
            },
            // Good ROAS (2.0 - 3.0) - Yellow
            {
                measure: 'ROAS',
                value1: thresholds.roasLow,
                value2: thresholds.roasGood,
                conditions: 'Between',
                style: {
                    backgroundColor: thresholds.colors.warning.bg,
                    color: thresholds.colors.warning.color,
                },
            },
            // Excellent ROAS (3.0 - 5.0) - Green
            {
                measure: 'ROAS',
                value1: thresholds.roasGood,
                value2: thresholds.roasExcellent,
                conditions: 'Between',
                style: {
                    backgroundColor: thresholds.colors.good.bg,
                    color: thresholds.colors.good.color,
                },
            },
            // Very High ROAS (> 5.0) - Dark Green
            {
                measure: 'ROAS',
                value1: thresholds.roasExcellent,
                conditions: 'GreaterThan',
                style: {
                    backgroundColor: thresholds.colors.excellent.bg,
                    color: thresholds.colors.excellent.color,
                    fontWeight: 'bold',
                },
            }
        );

        // TACOS formatting
        formatSettings.push(
            // High TACOS (> 30%) - Red
            {
                measure: 'TACOS',
                value1: this.alertThresholds.tacosHigh,
                conditions: 'GreaterThan',
                style: {
                    backgroundColor: '#ffebee',
                    color: '#c62828',
                    fontWeight: 'bold',
                },
            },
            // Medium TACOS (20% - 30%) - Yellow
            {
                measure: 'TACOS',
                value1: this.alertThresholds.tacosMedium,
                value2: this.alertThresholds.tacosHigh,
                conditions: 'Between',
                style: {
                    backgroundColor: '#fff8e1',
                    color: '#ef6c00',
                },
            }
        );

        return formatSettings;
    }

    // (Removed duplicate calculateCampaignMetricsInline/addCampaignClassificationsInline – standardized earlier definitions using ROAS/TACOS)

    getSmartTimeColumns() {
        try {
            // Prefer date range from page-level config or picker instances
            let from = null;
            let to = null;

            // Try Syncfusion DateRangePicker instance on Campaign tab
            const pickerEl = document.getElementById(
                'campaign-date-range-picker'
            );
            if (
                pickerEl &&
                pickerEl.ej2_instances &&
                pickerEl.ej2_instances[0]
            ) {
                const picker = pickerEl.ej2_instances[0];
                from = picker.startDate ? new Date(picker.startDate) : null;
                to = picker.endDate ? new Date(picker.endDate) : null;
            }

            // Fallback: page config embedded in DOM
            if (
                (!from || !to) &&
                window.gmvMaxConfig &&
                window.gmvMaxConfig.dateRange
            ) {
                from = new Date(window.gmvMaxConfig.dateRange.from);
                to = new Date(window.gmvMaxConfig.dateRange.to);
            }

            if (from && to) {
                return getSmartTimeGrouping({ from, to });
            }

            return [{ name: 'DateFormatted', caption: 'Ngày-Tháng-Năm' }];
        } catch (error) {
            console.error('Error in getSmartTimeColumns:', error);
            return [{ name: 'DateFormatted', caption: 'Ngày-Tháng-Năm' }];
        }
    }

    handleCellClick(args) {
        if (args.currentCell && args.data) {
            const cellData = args.data[0];
            if (!cellData) return;

            // Show alerts for poor performing campaigns
            if (
                cellData.ROASStatus === 'Critical' ||
                cellData.TACOSStatus === 'High'
            ) {
                this.showCampaignAlert(cellData);
            }
        }
    }

    showCampaignAlert(data) {
        const message = `
            <strong>⚠️ Cảnh báo hiệu suất chiến dịch!</strong><br>
            Chiến dịch: ${data.CampaignName}<br>
            ROI: ${data.ROAS}x (${data.ROASStatus})<br>
            TACOS: ${
                typeof data.TACOS === 'number'
                    ? (data.TACOS * 100).toFixed(1)
                    : data.TACOS
            }% (${data.TACOSStatus})<br>
            <em>Cần tối ưu hóa chiến dịch!</em>
        `;
        showToast('warning', message);
    }

    updateCampaignInsights() {
        // Dashboard is now independent of pivot table, no need to regenerate
        // Only update pivot table specific insights if needed
    }

    exportToExcel(fileName = 'TikTok_GMV_Max_Campaign_Analysis') {
        if (this.pivotTableObj) {
            this.pivotTableObj.excelExport({
                fileName: `${fileName}_${new Date()
                    .toISOString()
                    .slice(0, 10)}.xlsx`,
                includeHeader: true,
            });
        }
    }

    exportToPdf(fileName = 'TikTok_GMV_Max_Campaign_Report') {
        if (this.pivotTableObj) {
            this.pivotTableObj.pdfExport({
                fileName: `${fileName}_${new Date()
                    .toISOString()
                    .slice(0, 10)}.pdf`,
                includeHeader: true,
            });
        }
    }

    showChart() {
        if (this.pivotTableObj) {
            this.pivotTableObj.displayOption.view = 'Chart';
            this.pivotTableObj.chartSettings.chartSeries.type = 'Column';
            this.pivotTableObj.refresh();
        }
    }

    showGrid() {
        if (this.pivotTableObj) {
            this.pivotTableObj.displayOption.view = 'Grid';
            this.pivotTableObj.refresh();
        }
    }

    async refreshData(newData) {
        if (this.pivotTableObj && newData) {
            // Clear cached dashboard data to force refresh
            window.dashboardData = null;
            // ✅ PERFORMANCE: Use cached data processing and batched refresh
            const processedData = await this.extractPivotDataOptimized(
                ['campaign'],
                newData
            );
            this.batchedRefresh({ dataSource: processedData });
            this.updateCampaignInsights();
        }
    }

    // ✅ NEW: Update pivot table with column aggregations
    async updatePivotTableWithColumnAggregations() {
        if (!this.pivotTableObj) return;

        try {
            const currentAggregations = this.getStoredColumnAggregations();
            // Get configuration from merged localStorage structure
            const config = JSON.parse(
                localStorage.getItem('campaignColumnAggregations') || '{}'
            );
            const selectedValues = config.selectedFields || [
                'GrossRevenue',
                'Cost',
                'ROAS',
                'Orders',
                'CostPerOrder',
            ];

            if (selectedValues.length === 0) {
                console.warn('No values selected for pivot table');
                return;
            }

            // Create new value definitions with current column aggregations
            const allValues = await this.getCurrencyValues(currentAggregations);
            const newValues = allValues.filter((v) =>
                selectedValues.includes(v.name)
            );

            // ✅ PERFORMANCE: Use batched refresh for pivot table updates
            this.batchedRefresh({ values: newValues });
        } catch (error) {
            console.error(
                'Failed to update pivot table with aggregations:',
                error
            );
        }
    }
}
