using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service interface cho việc đồng bộ dữ liệu GMV Max Identities
    /// </summary>
    public interface IGmvMaxIdentitiesSyncService : ITransientDependency
    {
        /// <summary>
        /// Đồng bộ GMV Max Identities theo Advertiser ID và BC ID
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="unauthorizedAdvertiserIds">Danh sách advertiser IDs không có quyền (optional)</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<GmvMaxIdentitiesSyncResult> SyncGmvMaxIdentitiesAsync(string advertiserId, string bcId, List<string>? unauthorizedAdvertiserIds = null);

        /// <summary>
        /// Đồng bộ nhiều GMV Max Identities cho nhiều Advertiser
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="advertiserIds">Danh sách ID của Advertiser (null để đồng bộ tất cả)</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<GmvMaxIdentitiesSyncResult> SyncManyGmvMaxIdentitiesAsync(string bcId, List<string>? advertiserIds = null);

        /// <summary>
        /// Đồng bộ tất cả GMV Max Identities cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<GmvMaxIdentitiesSyncResult> SyncAllGmvMaxIdentitiesAsync();
    }

    /// <summary>
    /// Kết quả đồng bộ GMV Max Identities
    /// </summary>
    public class GmvMaxIdentitiesSyncResult : SyncResultBase
    {
        /// <summary>
        /// Tổng số bản ghi đã xử lý
        /// </summary>
        public override int TotalSynced => NewRecords + UpdatedRecords + ErrorRecords;

        /// <summary>
        /// Thời gian bắt đầu đồng bộ
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// Thời gian kết thúc đồng bộ
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Thời gian thực hiện (tính bằng giây)
        /// </summary>
        public double DurationSeconds => (EndTime - StartTime).TotalSeconds;

        /// <summary>
        /// Số Business Center đã đồng bộ
        /// </summary>
        public int BcCount { get; set; }

        /// <summary>
        /// Số Advertiser đã đồng bộ
        /// </summary>
        public int AdvertiserCount { get; set; }

        /// <summary>
        /// Số identities khả dụng cho Product GMV Max
        /// </summary>
        public int ProductGmvMaxAvailableCount { get; set; }

        /// <summary>
        /// Số identities khả dụng cho Live GMV Max
        /// </summary>
        public int LiveGmvMaxAvailableCount { get; set; }

        /// <summary>
        /// Số identities không khả dụng
        /// </summary>
        public int UnavailableCount { get; set; }

        /// <summary>
        /// Số identities đang chạy custom shop ads
        /// </summary>
        public int RunningCustomShopAdsCount { get; set; }

        /// <summary>
        /// Danh sách các loại identity đã đồng bộ
        /// </summary>
        public Dictionary<string, int> IdentityTypeCount { get; set; } = new Dictionary<string, int>();

        /// <summary>
        /// Danh sách lý do không khả dụng và số lượng
        /// </summary>
        public Dictionary<string, int> UnavailableReasonCount { get; set; } = new Dictionary<string, int>();

        /// <summary>
        /// Danh sách advertiser IDs không có quyền truy cập
        /// </summary>
        public List<string> UnauthorizedAdvertiserIds { get; set; } = new List<string>();
    }
}
