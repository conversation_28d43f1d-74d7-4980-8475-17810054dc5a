@page
@using Volo.Abp.Account.Settings
@using Volo.Abp.Settings
@model TikTok.Web.Pages.Account.CustomLoginModel
@using Microsoft.AspNetCore.Mvc.Localization
@using Volo.Abp.Account.Localization
@inject IHtmlLocalizer<AccountResource> L
@inject Volo.Abp.Settings.ISettingProvider SettingProvider
@{
    ViewData["Title"] = L["Login"];
    Layout = null;
}

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - TikTok Ads Manager</title>
    
    <!-- CSS Files -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="stylesheet" href="/css/account/login.css" />
</head>
<body>

<div class="login-container">
    <!-- Left Side - Login Form -->
    <div class="login-left-section">
        <!-- Logo Section -->
        <div class="logo-section">
            <img src="/images/logo/leptonx/logo-dark.png" alt="Logo" class="logo-image" />
        </div>
        
        <!-- Subtitle -->
        <p class="subtitle">Tối ưu chiến dịch GMV Max của bạn với công cụ chuyên nghiệp</p>
        
        <!-- Login Form Card -->
        <div class="login-form-section">
            <!-- Form Header -->
            <div class="form-header">
                <h1 class="form-title">Đăng nhập</h1>
            </div>
        @if (Model.EnableLocalLogin)
        {
            <form action="/Account/Login" method="post">
                @Html.AntiForgeryToken()
                <input name="ReturnUrl" type="hidden" value="@Model.ReturnUrl" />
                <input name="ReturnUrlHash" type="hidden" value="@Model.ReturnUrlHash" />
                
                <div class="form-group">
                    <label for="UserNameOrEmailAddress" class="form-label">Tên đăng nhập hoặc email</label>
                    <input name="LoginInput.UserNameOrEmailAddress" id="UserNameOrEmailAddress" class="form-control" placeholder="Nhập tên đăng nhập hoặc email" required />
                    <span class="text-danger" id="username-error"></span>
                </div>
                
                <div class="form-group">
                    <label for="Password" class="form-label">Mật khẩu</label>
                    <input name="LoginInput.Password" id="Password" type="password" class="form-control" placeholder="Nhập mật khẩu" required />
                    <span class="text-danger" id="password-error"></span>
                </div>
                
                <div class="form-options">
                    <div class="remember-me">
                        <input name="LoginInput.RememberMe" id="RememberMe" type="checkbox" />
                        <label for="RememberMe">Ghi nhớ đăng nhập</label>
                    </div>
                    <a href="#" class="forgot-password">Quên mật khẩu?</a>
                </div>
                
                <button type="submit" name="Action" value="Login" class="btn-login">
                    Đăng nhập
                </button>
            </form>
            
            @if (await SettingProvider.IsTrueAsync(AccountSettingNames.IsSelfRegistrationEnabled))
            {
                <div class="register-section">
                    <span>Chưa có tài khoản? </span>
                    <a href="@Url.Page("./Register", new {returnUrl = Model.ReturnUrl, returnUrlHash = Model.ReturnUrlHash})" class="register-link">Đăng ký ngay</a>
                </div>
            }
        }
        
        @if (Model.VisibleExternalProviders.Any())
        {
            <div style="margin-top: 24px; text-align: center;">
                <form action="/Account/ExternalLogin" method="post">
                    @Html.AntiForgeryToken()
                    <input name="ReturnUrl" type="hidden" value="@Model.ReturnUrl" />
                    <input name="ReturnUrlHash" type="hidden" value="@Model.ReturnUrlHash" />
                    @foreach (var provider in Model.VisibleExternalProviders)
                    {
                        <button type="submit" style="margin: 8px; padding: 16px; border: 1px solid #e5e5e5; background: #ffffff; border-radius: 8px;" name="provider" value="@provider.AuthenticationScheme">
                            @provider.DisplayName
                        </button>
                    }
                </form>
            </div>
        }
        
        @if (!Model.EnableLocalLogin && !Model.VisibleExternalProviders.Any())
        {
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 16px; border-radius: 8px; margin-top: 24px;">
                <strong>@L["InvalidLoginRequest"]</strong><br>
                @L["ThereAreNoLoginSchemesConfiguredForThisClient"]
            </div>
        }
        </div>
    </div>
</div>

<!-- JavaScript Files -->
<script src="/js/account/login.js"></script>

</body>
</html>
