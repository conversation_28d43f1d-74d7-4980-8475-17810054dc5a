/* Login Page Design - Exact Match */
:root {
    /* Colors from design */
    --bg-cream: #f5f1eb;
    --primary-orange: #e87b35;
    --primary-orange-hover: #d16b25;
    --text-dark: #2c2c2c;
    --text-gray: #666666;
    --text-light-gray: #999999;
    --white: #ffffff;
    --input-border: #e5e5e5;
    --input-focus: #e87b35;

    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI',
        Roboto, sans-serif;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;

    /* <PERSON> Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;

    /* Shadows */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--bg-cream);
    background-image: url('/images/backgrounds/login-bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md);
    color: var(--text-dark);
    line-height: 1.5;
}

.login-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    min-height: 60vh;
    padding: var(--spacing-lg) 0;
}

/* Left Side - Container */
.login-left-section {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 500px;
}

/* Login Form Card */
.login-form-section {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    margin-top: var(--spacing-md);
}

/* Logo Section */
.logo-section {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-md);
}

.logo-image {
    height: 80px;
    width: auto;
    object-fit: contain;
}

.subtitle {
    font-size: 16px;
    color: var(--text-black);
    font-weight: var(--font-weight-medium);
    margin-bottom: 0;
    line-height: 1.4;
    text-align: center;
}

/* Form Header */
.form-header {
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

.form-title {
    font-size: 28px;
    font-weight: var(--font-weight-bold);
    color: var(--primary-orange);
    margin-bottom: var(--spacing-sm);
}

/* Form Groups */
.form-group {
    margin-bottom: var(--spacing-lg);
    position: relative;
}

.form-label {
    display: block;
    font-size: 14px;
    font-weight: var(--font-weight-medium);
    color: var(--text-dark);
    margin-bottom: var(--spacing-sm);
}

.form-control {
    width: 100%;
    padding: 14px 16px;
    border: 1px solid var(--input-border);
    border-radius: var(--radius-md);
    font-size: 14px;
    font-family: var(--font-family);
    background: var(--white);
    transition: all 0.2s ease;
    box-sizing: border-box;
}

.form-control::placeholder {
    color: var(--text-light-gray);
    font-weight: var(--font-weight-normal);
}

.form-control:focus {
    outline: none;
    border-color: var(--input-focus);
    box-shadow: 0 0 0 3px rgba(232, 123, 53, 0.1);
}

.form-control.error {
    border-color: var(--primary-orange);
}

/* Validation styling */
.text-danger {
    color: var(--primary-orange);
    font-size: 12px;
    font-weight: var(--font-weight-medium);
    margin-top: var(--spacing-xs);
    display: block;
    line-height: 1.3;
}

/* Password toggle button */
.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    background: none;
    border: none;
    color: var(--text-gray);
    cursor: pointer;
    font-size: 16px;
    padding: 4px;
    transition: color 0.2s ease;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
}

.password-toggle:hover {
    color: var(--primary-orange);
}

.password-toggle:focus {
    outline: none;
    color: var(--primary-orange);
}

/* Remember Me & Forgot Password */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    font-size: 14px;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-dark);
}

.remember-me input[type='checkbox'] {
    width: 16px;
    height: 16px;
    accent-color: var(--primary-orange);
    cursor: pointer;
}

.remember-me label {
    font-size: 14px;
    color: var(--text-dark);
    cursor: pointer;
    margin: 0;
}

.forgot-password {
    color: var(--primary-orange);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
}

.forgot-password:hover {
    text-decoration: underline;
}

/* Login Button */
.btn-login {
    width: 100%;
    padding: 16px 24px;
    background: var(--primary-orange);
    color: var(--white);
    border: none;
    border-radius: var(--radius-md);
    font-size: 16px;
    font-weight: var(--font-weight-semibold);
    font-family: var(--font-family);
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(232, 123, 53, 0.2);
    margin-top: var(--spacing-md);
}

.btn-login:hover {
    background: var(--primary-orange-hover);
    box-shadow: 0 4px 8px rgba(232, 123, 53, 0.3);
    transform: translateY(-1px);
}

.btn-login:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(232, 123, 53, 0.2);
}

/* Register Link */
.register-section {
    text-align: center;
    margin-top: var(--spacing-xl);
    font-size: 14px;
    color: var(--text-gray);
}

.register-link {
    color: var(--primary-orange);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
}

.register-link:hover {
    text-decoration: underline;
}

/* Removed illustration section - no longer needed */

/* Responsive Design */

@media (max-width: 1024px) {
    .login-container {
        justify-content: center;
        max-width: 500px;
        min-height: 50vh;
        padding: var(--spacing-md) 0;
    }

    .logo-image {
        height: 70px;
    }

    body {
        padding: var(--spacing-sm);
    }
}

@media (max-width: 768px) {
    body {
        padding: var(--spacing-md);
        align-items: flex-start;
    }

    .login-container {
        justify-content: center;
        min-height: auto;
        padding: var(--spacing-md) 0;
        width: 100%;
        margin: 0 auto;
    }

    .login-left-section {
        width: 100%;
        max-width: 400px;
    }

    .login-form-section {
        padding: var(--spacing-xl);
        margin-top: var(--spacing-md);
    }

    .form-title {
        font-size: 24px;
        margin-bottom: var(--spacing-lg);
    }

    .logo-image {
        height: 60px;
    }

    .logo-section {
        margin-bottom: var(--spacing-md);
    }

    .form-options {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: flex-start;
    }

    .form-group {
        margin-bottom: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    body {
        padding: var(--spacing-sm);
    }

    .login-container {
        padding: var(--spacing-sm) 0;
    }

    .login-left-section {
        max-width: 350px;
    }

    .login-form-section {
        padding: var(--spacing-lg);
        margin-top: var(--spacing-sm);
    }

    .form-title {
        font-size: 22px;
        margin-bottom: var(--spacing-md);
    }

    .logo-image {
        height: 55px;
    }

    .subtitle {
        font-size: 15px;
        margin-bottom: var(--spacing-md);
    }

    .btn-login {
        padding: 14px 24px;
        font-size: 15px;
    }

    .form-group {
        margin-bottom: var(--spacing-md);
    }

    .form-label {
        font-size: 15px;
        margin-bottom: var(--spacing-sm);
    }

    .form-control {
        padding: 12px 16px;
        font-size: 15px;
    }
}

/* Special handling for register page with more fields */
@media (max-width: 768px) {
    /* Compact form for register page */
    .register-page .login-form-section {
        padding: var(--spacing-lg);
    }

    .register-page .form-group {
        margin-bottom: var(--spacing-md);
    }

    .register-page .logo-image {
        height: 55px;
    }

    .register-page .subtitle {
        font-size: 15px;
        margin-bottom: var(--spacing-md);
    }

    .register-page .form-title {
        margin-bottom: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .register-page .login-form-section {
        padding: var(--spacing-md);
    }

    .register-page .form-group {
        margin-bottom: var(--spacing-sm);
    }

    .register-page .logo-image {
        height: 50px;
    }

    .register-page .form-title {
        font-size: 20px;
        margin-bottom: var(--spacing-sm);
    }

    .register-page .subtitle {
        font-size: 14px;
        margin-bottom: var(--spacing-sm);
    }
}

/* Very small screens (iPhone SE, small Android) */
@media (max-width: 375px) {
    body {
        padding: var(--spacing-xs);
    }

    .login-left-section {
        max-width: 320px;
    }

    .login-form-section {
        padding: var(--spacing-md);
    }

    .form-title {
        font-size: 20px;
    }

    .logo-image {
        height: 50px;
    }

    .register-page .login-form-section {
        padding: var(--spacing-sm);
    }

    .register-page .form-group {
        margin-bottom: var(--spacing-xs);
    }
}

/* Landscape mobile optimization */
@media (max-height: 600px) and (orientation: landscape) {
    body {
        align-items: flex-start;
        padding: var(--spacing-xs);
    }

    .login-container {
        min-height: auto;
        padding: var(--spacing-xs) 0;
    }

    .logo-image {
        height: 40px;
    }

    .logo-section {
        margin-bottom: var(--spacing-xs);
    }

    .login-form-section {
        padding: var(--spacing-md);
        margin-top: var(--spacing-xs);
    }

    .form-group {
        margin-bottom: var(--spacing-xs);
    }

    .subtitle {
        font-size: 14px;
        margin-bottom: var(--spacing-xs);
    }

    .form-title {
        font-size: 18px;
        margin-bottom: var(--spacing-sm);
    }
}
