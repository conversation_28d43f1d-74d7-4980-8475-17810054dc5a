using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Domain.Entities.Auditing;

namespace TikTok.Entities
{
    /// <summary>
    /// Entity đại diện cho sản phẩm TikTok Shop từ API.
    /// Ch<PERSON>a thông tin chi tiết về các sản phẩm có sẵn trong TikTok Shop và trạng thái GMV Max của chúng.
    /// </summary>
    public class RawTikTokShopProductsEntity : AuditedEntity<Guid>
    {
        public RawTikTokShopProductsEntity()
        {
            
        }

        public RawTikTokShopProductsEntity(Guid id) : base(id)
        {
        }

        /// <summary>
        /// ID Business Center
        /// </summary>
        [Required]
        [StringLength(100)]
        public string BcId { get; set; }

        /// <summary>
        /// ID nhà quảng cáo
        /// </summary>
        [Required]
        [StringLength(100)]
        public string AdvertiserId { get; set; }

        /// <summary>
        /// ID của TikTok Shop
        /// </summary>
        [Required]
        [StringLength(100)]
        public string StoreId { get; set; }

        /// <summary>
        /// ID nhóm sản phẩm (item_group_id)
        /// </summary>
        [Required]
        [StringLength(100)]
        public string ItemGroupId { get; set; }

        /// <summary>
        /// Trạng thái sản phẩm (AVAILABLE, UNAVAILABLE, etc.)
        /// </summary>
        [StringLength(50)]
        public string? Status { get; set; }

        /// <summary>
        /// URL hình ảnh sản phẩm
        /// </summary>
        [StringLength(500)]
        public string? ProductImageUrl { get; set; }

        /// <summary>
        /// ID catalog (có thể null)
        /// </summary>
        [StringLength(100)]
        public string? CatalogId { get; set; }

        /// <summary>
        /// Danh mục sản phẩm
        /// </summary>
        [StringLength(200)]
        public string? Category { get; set; }

        /// <summary>
        /// Số lượng bán lịch sử
        /// </summary>
        public int HistoricalSales { get; set; }

        /// <summary>
        /// Giá tối đa (dạng string từ API)
        /// </summary>
        [StringLength(50)]
        public string? MaxPrice { get; set; }

        /// <summary>
        /// Giá tối thiểu (dạng string từ API)
        /// </summary>
        [StringLength(50)]
        public string? MinPrice { get; set; }

        /// <summary>
        /// Đơn vị tiền tệ
        /// </summary>
        [StringLength(10)]
        public string? Currency { get; set; }

        /// <summary>
        /// Tiêu đề sản phẩm
        /// </summary>
        [StringLength(500)]
        public string? Title { get; set; }

        /// <summary>
        /// Trạng thái GMV Max Ads (OCCUPIED, AVAILABLE, etc.)
        /// </summary>
        [StringLength(50)]
        public string? GmvMaxAdsStatus { get; set; }

        /// <summary>
        /// Có đang chạy custom shop ads không
        /// </summary>
        public bool IsRunningCustomShopAds { get; set; }

        /// <summary>
        /// Thời gian đồng bộ cuối cùng
        /// </summary>
        public DateTime? SyncedAt { get; set; }

        /// <summary>
        /// Kiểm tra xem entity hiện tại có khác với dữ liệu mới không
        /// </summary>
        /// <param name="newData">Dữ liệu mới để so sánh</param>
        /// <returns>True nếu có thay đổi</returns>
        public bool HasChanged(RawTikTokShopProductsEntity newData)
        {
            return Status != newData.Status ||
                   ProductImageUrl != newData.ProductImageUrl ||
                   CatalogId != newData.CatalogId ||
                   Category != newData.Category ||
                   HistoricalSales != newData.HistoricalSales ||
                   MaxPrice != newData.MaxPrice ||
                   MinPrice != newData.MinPrice ||
                   Currency != newData.Currency ||
                   Title != newData.Title ||
                   GmvMaxAdsStatus != newData.GmvMaxAdsStatus ||
                   IsRunningCustomShopAds != newData.IsRunningCustomShopAds;
        }

        /// <summary>
        /// Cập nhật entity hiện tại với dữ liệu mới
        /// </summary>
        /// <param name="newData">Dữ liệu mới</param>
        public void UpdateFromNewData(RawTikTokShopProductsEntity newData)
        {
            Status = newData.Status;
            ProductImageUrl = newData.ProductImageUrl;
            CatalogId = newData.CatalogId;
            Category = newData.Category;
            HistoricalSales = newData.HistoricalSales;
            MaxPrice = newData.MaxPrice;
            MinPrice = newData.MinPrice;
            Currency = newData.Currency;
            Title = newData.Title;
            GmvMaxAdsStatus = newData.GmvMaxAdsStatus;
            IsRunningCustomShopAds = newData.IsRunningCustomShopAds;
        }
    }
}
