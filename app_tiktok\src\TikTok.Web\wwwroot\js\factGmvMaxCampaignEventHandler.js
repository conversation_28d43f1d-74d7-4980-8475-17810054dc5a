$(function () {
    // ✅ THÊM: Ranking Loading Manager
    class RankingLoadingManager {
        static showCostRankingLoading() {
            LoadingManager.showLoading(
                'cost-ranking-loading',
                'Đang tải xếp hạng chi tiêu...',
                'danger'
            );
        }

        static hideCostRankingLoading(content = '') {
            LoadingManager.hideLoading('cost-ranking-loading', content);
        }

        static showRevenueRankingLoading() {
            LoadingManager.showLoading(
                'revenue-ranking-loading',
                'Đang tải xếp hạng doanh thu...',
                'danger'
            );
        }

        static hideRevenueRankingLoading(content = '') {
            LoadingManager.hideLoading('revenue-ranking-loading', content);
        }

        static showRankingError(
            rankingType,
            message = 'Lỗi tải dữ liệu xếp hạng'
        ) {
            const containerId =
                rankingType === 'cost'
                    ? 'cost-ranking-loading'
                    : 'revenue-ranking-loading';
            LoadingManager.showError(containerId, message);
        }
    }

    // ✅ PERFORMANCE: Debounce utility function to prevent rapid multiple calls
    function debounce(func, wait, immediate) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                timeout = null;
                if (!immediate) func.apply(this, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(this, args);
        };
    }

    // Event handlers for GMV Max Campaign Dashboard - Optimized with debounce

    $('#refresh-data').click(async function () {
        // ✅ FIXED: Chỉ refresh pivot table, không ảnh hưởng các phần khác
        if (window.tiktokGmvMaxCampaignPivotTable) {
            try {
                // ✅ FIXED: Loading sẽ được handle trong refreshPivotDataWithFilters()
                await window.tiktokGmvMaxCampaignPivotTable.refreshPivotDataWithFilters();

                if (typeof abp !== 'undefined' && abp.notify) {
                    abp.notify.success(
                        'Bảng phân tích đã được làm mới thành công!'
                    );
                } else {
                    console.log(
                        '✅ SUCCESS: Bảng phân tích đã được làm mới thành công!'
                    );
                }
            } catch (error) {
                if (typeof abp !== 'undefined' && abp.notify) {
                    abp.notify.error('Lỗi làm mới dữ liệu: ' + error.message);
                } else {
                    console.error(
                        '❌ ERROR: Lỗi làm mới dữ liệu: ' + error.message
                    );
                }
            }
        } else {
            if (typeof abp !== 'undefined' && abp.notify) {
                abp.notify.warn('Pivot table chưa được khởi tạo');
            } else {
                console.warn('⚠️ WARNING: Pivot table chưa được khởi tạo');
            }
        }
    });

    $('#export-excel').click(function () {
        if (window.tiktokGmvMaxCampaignPivotTable) {
            // ✅ Use smart export for full data
            window.tiktokGmvMaxCampaignPivotTable.exportToExcelSmart();
        }
    });

    $('#export-pdf').click(function () {
        if (window.tiktokGmvMaxCampaignPivotTable) {
            window.tiktokGmvMaxCampaignPivotTable.exportToPdf();
            if (typeof abp !== 'undefined' && abp.notify) {
                abp.notify.success('Đã xuất dữ liệu chiến dịch sang PDF!');
            } else {
                console.log('✅ SUCCESS: Đã xuất dữ liệu chiến dịch sang PDF!');
            }
        }
    });

    $('#toggle-chart').click(function () {
        if (window.tiktokGmvMaxCampaignPivotTable) {
            const currentView =
                window.tiktokGmvMaxCampaignPivotTable.pivotTableObj
                    .displayOption.view;
            if (currentView === 'Grid' || currentView === 'Table') {
                window.tiktokGmvMaxCampaignPivotTable.showChart();
                $(this).html('<i class="fas fa-table"></i> Chuyển sang bảng');
            } else {
                window.tiktokGmvMaxCampaignPivotTable.showGrid();
                $(this).html(
                    '<i class="fas fa-chart-bar"></i> Chuyển sang biểu đồ'
                );
            }
        }
    });

    // ✅ Enhanced filter system with multiselect support

    // ✅ PERFORMANCE: Initialize filter data arrays and Syncfusion components with lazy loading
    let availableBusinessCenters = [];
    let availableShops = [];
    let businessCenterMultiSelect,
        shopMultiSelect,
        campaignTypeMultiSelect,
        performanceMultiSelect,
        dateRangePicker,
        quickDateDropdown;

    // ✅ PERFORMANCE: Track initialization state to avoid redundant setup
    let filtersInitialized = false;
    let syncfusionReady = false;
    let isInitializing = false; // ✅ FIXED: Prevent multiple initialization calls
    let retryCount = 0; // ✅ FIXED: Prevent infinite retry loops
    let dataLoaded = false; // ✅ NEW: Track data loading state
    let initializationPromise = null; // ✅ NEW: Prevent concurrent initialization

    // ✅ PERFORMANCE: Optimized lazy populate filter options with better error handling
    async function populateFilterOptions() {
        // ✅ NEW: Prevent concurrent initialization
        if (initializationPromise) {
            return initializationPromise;
        }

        if (filtersInitialized || isInitializing) return;

        // ✅ FIXED: Double-check Syncfusion availability before proceeding
        if (!syncfusionReady) {
            retryCount++;
            if (retryCount <= 3) {
                // ✅ REDUCED: From 5 to 3 retries
                setTimeout(async () => await populateFilterOptions(), 300); // ✅ INCREASED: From 200ms to 300ms
            } else {
                retryCount = 0;
            }
            return;
        }

        retryCount = 0; // ✅ FIXED: Reset retry count on success

        // ✅ NEW: Create initialization promise to prevent concurrent calls
        initializationPromise = (async () => {
            isInitializing = true; // ✅ FIXED: Prevent multiple calls

            // ✅ PERFORMANCE: Show loading indicator while loading data
            const loadingIndicator = document.getElementById('filters-loading');
            if (loadingIndicator) {
                loadingIndicator.style.display = 'block';
            }

            try {
                // ✅ OPTIMIZED: Load data only if not already loaded
                if (!dataLoaded && !window.initialGmvMaxCampaignData) {
                    if (window.loadCampaignDataForFilters) {
                        await window.loadCampaignDataForFilters();
                        dataLoaded = true;
                    }
                }

                if (!window.initialGmvMaxCampaignData) {
                    // Initialize with empty data to make components work
                    window.initialGmvMaxCampaignData = {
                        dimBusinessCenters: [],
                        dimStores: [],
                        factGmvMaxCampaigns: [],
                    };
                }
            } catch (error) {
                console.error('❌ Error loading filter data:', error);
                // Initialize with empty data on error
                window.initialGmvMaxCampaignData = {
                    dimBusinessCenters: [],
                    dimStores: [],
                    factGmvMaxCampaigns: [],
                };
            } finally {
                // ✅ PERFORMANCE: Hide loading indicator and reset initialization flag
                if (loadingIndicator) {
                    loadingIndicator.style.display = 'none';
                }
                isInitializing = false; // ✅ FIXED: Reset flag
                initializationPromise = null; // ✅ NEW: Clear promise
            }
        })();

        // ✅ NEW: Continue with data processing after initialization
        await initializationPromise;

        // Populating filter options
        const data = window.initialGmvMaxCampaignData;

        // ✅ FIXED: Check both lowercase and uppercase property names
        const businessCenters =
            data.dimBusinessCenters || data.DimBusinessCenters;
        const stores = data.dimStores || data.DimStores;

        // ✅ PERFORMANCE: Optimize data processing with batch operations
        const processData = () => {
            // Extract unique business centers
            if (businessCenters && businessCenters.length > 0) {
                availableBusinessCenters = [
                    ...new Map(
                        businessCenters.map((bc) => [
                            bc.id || bc.Id,
                            {
                                value: bc.id || bc.Id,
                                text:
                                    bc.bcName ||
                                    bc.BcName ||
                                    bc.name ||
                                    bc.Name,
                            },
                        ])
                    ).values(),
                ];
            }

            // Extract unique shops
            if (stores && stores.length > 0) {
                availableShops = [
                    ...new Map(
                        stores.map((store) => [
                            store.id || store.Id,
                            {
                                value: store.id || store.Id,
                                text:
                                    store.storeName ||
                                    store.StoreName ||
                                    store.name ||
                                    store.Name,
                            },
                        ])
                    ).values(),
                ];
            }
        };

        // ✅ PERFORMANCE: Use requestAnimationFrame for smooth UI
        requestAnimationFrame(() => {
            processData();

            // ✅ PERFORMANCE: Initialize components after data processing
            setTimeout(() => {
                // ✅ FIXED: Ensure DOM elements are ready before initializing Syncfusion
                const datePickerElement =
                    document.getElementById('date-range-picker');
                const businessCenterElement = document.getElementById(
                    'business-center-multiselect'
                );
                const shopElement = document.getElementById('shop-multiselect');

                if (datePickerElement && businessCenterElement && shopElement) {
                    initializeSyncfusionComponents();
                    filtersInitialized = true;
                } else {
                    setTimeout(() => {
                        initializeSyncfusionComponents();
                        filtersInitialized = true;
                    }, 100);
                }
            }, 10);
        });
    }

    // ✅ OPTIMIZED: Initialize all Syncfusion components with better error handling
    function initializeSyncfusionComponents() {
        // ✅ FIXED: Double-check Syncfusion availability before initializing components
        if (!syncfusionReady) {
            console.warn(
                '⚠️ Syncfusion not ready, skipping component initialization'
            );
            return;
        }

        try {
            // ✅ PERFORMANCE: Initialize components in sequence to avoid conflicts
            initializeDateComponents();

            // ✅ PERFORMANCE: Add small delay between component initializations
            setTimeout(() => {
                initializeSyncfusionMultiSelects();
            }, 50);
        } catch (error) {
            console.error(
                '❌ Error initializing Syncfusion components:',
                error
            );
        }
    }

    // ✅ OPTIMIZED: Initialize Date Range Picker and Quick Date DropDown with better performance
    function initializeDateComponents() {
        try {
            // Check if element exists
            const datePickerElement =
                document.getElementById('date-range-picker');
            if (!datePickerElement) {
                console.warn('⚠️ Date range picker element not found');
                return;
            }

            // ✅ FIXED: Check if Syncfusion is available
            if (
                typeof ej === 'undefined' ||
                !ej.calendars ||
                !ej.calendars.DateRangePicker
            ) {
                console.warn('⚠️ Syncfusion DateRangePicker not available');
                return;
            }

            // ✅ FIXED: Use simple initialization pattern like FactGmvMaxProduct (NO startDate/endDate)
            try {
                dateRangePicker = new ej.calendars.DateRangePicker({
                    placeholder: 'Chọn khoảng thời gian',
                    locale: 'vi-VN',
                    change: function (args) {
                        // ✅ OPTIMIZED: Không cần lưu vào filterState, chỉ cần DateRangePicker
                        // Date range changed, user needs to click Apply
                    },
                });

                // ✅ FIXED: Validate DateRangePicker object before appending
                if (
                    dateRangePicker &&
                    typeof dateRangePicker.appendTo === 'function'
                ) {
                    dateRangePicker.appendTo('#date-range-picker');

                    // ✅ Store DateRangePicker globally for access from pivot table classes
                    window.dateRangePicker = dateRangePicker;
                    console.log(
                        '✅ DateRangePicker initialized successfully (simple pattern)'
                    );
                } else {
                    console.error('❌ DateRangePicker object is invalid');
                    return;
                }
            } catch (initError) {
                console.error('❌ Error creating DateRangePicker:', initError);
                return;
            }
        } catch (error) {
            console.error('❌ Error initializing DateRangePicker:', error);
        }

        // ✅ FIXED: Quick Date DropDown with better error handling
        try {
            const quickDateData = [
                { value: '', text: 'Chọn khoảng' },
                { value: 'today', text: 'Hôm nay' },
                { value: 'week', text: '7 ngày' },
                { value: 'month', text: '30 ngày' },
                { value: 'quarter', text: '90 ngày' },
            ];

            // ✅ FIXED: Validate dataSource before creating DropDownList
            if (
                !quickDateData ||
                !Array.isArray(quickDateData) ||
                quickDateData.length === 0
            ) {
                console.error('❌ Invalid quickDateData for DropDownList');
                return;
            }

            // ✅ MINIMAL CONFIG: Quick Date DropDown - chỉ giữ essentials
            quickDateDropdown = new ej.dropdowns.DropDownList({
                dataSource: quickDateData,
                fields: { text: 'text', value: 'value' },
                placeholder: 'Chọn khoảng',
                change: function (args) {
                    try {
                        if (args && args.value) {
                            setQuickDateFilter(args.value);
                        }
                    } catch (error) {
                        console.error(
                            '❌ Error in quick date filter change:',
                            error
                        );
                    }
                },
            });

            // ✅ FIXED: Validate DropDownList object before appending
            if (
                quickDateDropdown &&
                typeof quickDateDropdown.appendTo === 'function'
            ) {
                quickDateDropdown.appendTo('#quick-date-dropdown');
                console.log('✅ Quick Date DropDown initialized successfully');
            } else {
                console.error('❌ Quick Date DropDown object is invalid');
            }
        } catch (dropdownError) {
            console.error(
                '❌ Error initializing Quick Date DropDown:',
                dropdownError
            );
        }
    }

    // ✅ OPTIMIZED: Initialize Syncfusion MultiSelect components with better performance
    function initializeSyncfusionMultiSelects() {
        // ✅ FIXED: Check if Syncfusion is available
        if (
            typeof ej === 'undefined' ||
            !ej.dropdowns ||
            !ej.dropdowns.MultiSelect
        ) {
            console.warn('⚠️ Syncfusion MultiSelect not available');
            return;
        }

        try {
            // ✅ FIXED: Business Center MultiSelect with better error handling
            const businessCenterElement = document.getElementById(
                'business-center-multiselect'
            );
            if (businessCenterElement) {
                try {
                    // ✅ FIXED: Validate dataSource before creating MultiSelect
                    const bcDataSource = availableBusinessCenters || [];
                    if (!Array.isArray(bcDataSource)) {
                        console.warn(
                            '⚠️ availableBusinessCenters is not an array, using empty array'
                        );
                        availableBusinessCenters = [];
                    }

                    businessCenterMultiSelect = new ej.dropdowns.MultiSelect({
                        dataSource: availableBusinessCenters,
                        fields: { text: 'text', value: 'value' },
                        placeholder: 'Chọn trung tâm kinh doanh',
                        mode: 'Default',
                        showClearButton: true,
                        enableFiltering: true,
                        // ✅ NEW: Performance optimizations
                        enablePersistence: false,
                        enableVirtualization:
                            availableBusinessCenters.length > 100,
                        cssClass: 'custom-multiselect',
                    });

                    // ✅ FIXED: Validate MultiSelect object before appending
                    if (
                        businessCenterMultiSelect &&
                        typeof businessCenterMultiSelect.appendTo === 'function'
                    ) {
                        businessCenterMultiSelect.appendTo(
                            '#business-center-multiselect'
                        );
                        console.log(
                            '✅ Business Center MultiSelect initialized successfully'
                        );
                    } else {
                        console.error(
                            '❌ Business Center MultiSelect object is invalid'
                        );
                    }
                } catch (bcError) {
                    console.error(
                        '❌ Error initializing Business Center MultiSelect:',
                        bcError
                    );
                }
            }

            // ✅ FIXED: Shop MultiSelect with better error handling
            const shopElement = document.getElementById('shop-multiselect');
            if (shopElement) {
                try {
                    // ✅ FIXED: Validate dataSource before creating MultiSelect
                    const shopDataSource = availableShops || [];
                    if (!Array.isArray(shopDataSource)) {
                        console.warn(
                            '⚠️ availableShops is not an array, using empty array'
                        );
                        availableShops = [];
                    }

                    shopMultiSelect = new ej.dropdowns.MultiSelect({
                        dataSource: availableShops,
                        fields: { text: 'text', value: 'value' },
                        placeholder: 'Chọn shop',
                        mode: 'Default',
                        showClearButton: true,
                        enableFiltering: true,
                        // ✅ NEW: Performance optimizations
                        enablePersistence: false,
                        enableVirtualization: availableShops.length > 100,
                        cssClass: 'custom-multiselect',
                    });

                    // ✅ FIXED: Validate MultiSelect object before appending
                    if (
                        shopMultiSelect &&
                        typeof shopMultiSelect.appendTo === 'function'
                    ) {
                        shopMultiSelect.appendTo('#shop-multiselect');
                        console.log(
                            '✅ Shop MultiSelect initialized successfully'
                        );
                    } else {
                        console.error('❌ Shop MultiSelect object is invalid');
                    }
                } catch (shopError) {
                    console.error(
                        '❌ Error initializing Shop MultiSelect:',
                        shopError
                    );
                }
            }

            // ✅ FIXED: Campaign Type MultiSelect with better error handling
            const campaignTypeData = [
                { value: 'PRODUCT', text: 'Sản phẩm' },
                { value: 'LIVE', text: 'Live Stream' },
            ];

            const campaignTypeElement = document.getElementById(
                'campaign-type-multiselect'
            );
            if (campaignTypeElement) {
                try {
                    // ✅ FIXED: Validate dataSource before creating MultiSelect
                    if (
                        !Array.isArray(campaignTypeData) ||
                        campaignTypeData.length === 0
                    ) {
                        console.error(
                            '❌ Invalid campaignTypeData for MultiSelect'
                        );
                        return;
                    }

                    campaignTypeMultiSelect = new ej.dropdowns.MultiSelect({
                        dataSource: campaignTypeData,
                        fields: { text: 'text', value: 'value' },
                        placeholder: 'Chọn loại chiến dịch',
                        mode: 'Default',
                        showClearButton: true,
                        // ✅ NEW: Performance optimizations
                        enablePersistence: false,
                        cssClass: 'custom-multiselect',
                    });

                    // ✅ FIXED: Validate MultiSelect object before appending
                    if (
                        campaignTypeMultiSelect &&
                        typeof campaignTypeMultiSelect.appendTo === 'function'
                    ) {
                        campaignTypeMultiSelect.appendTo(
                            '#campaign-type-multiselect'
                        );
                        console.log(
                            '✅ Campaign Type MultiSelect initialized successfully'
                        );
                    } else {
                        console.error(
                            '❌ Campaign Type MultiSelect object is invalid'
                        );
                    }
                } catch (campaignTypeError) {
                    console.error(
                        '❌ Error initializing Campaign Type MultiSelect:',
                        campaignTypeError
                    );
                }
            }

            // ✅ FIXED: Performance MultiSelect with better error handling
            const performanceData = [
                { value: 'excellent', text: 'Xuất sắc (ROI > 3.0)' },
                { value: 'good', text: 'Tốt (ROI 2.0-3.0)' },
                { value: 'average', text: 'Trung bình (ROI 1.5-2.0)' },
                { value: 'poor', text: 'Kém (ROI < 1.5)' },
            ];

            const performanceElement = document.getElementById(
                'performance-multiselect'
            );
            if (performanceElement) {
                try {
                    // ✅ FIXED: Validate dataSource before creating MultiSelect
                    if (
                        !Array.isArray(performanceData) ||
                        performanceData.length === 0
                    ) {
                        console.error(
                            '❌ Invalid performanceData for MultiSelect'
                        );
                        return;
                    }

                    performanceMultiSelect = new ej.dropdowns.MultiSelect({
                        dataSource: performanceData,
                        fields: { text: 'text', value: 'value' },
                        placeholder: 'Chọn mức hiệu suất',
                        mode: 'Default',
                        showClearButton: true,
                        // ✅ NEW: Performance optimizations
                        enablePersistence: false,
                        cssClass: 'custom-multiselect',
                    });

                    // ✅ FIXED: Validate MultiSelect object before appending
                    if (
                        performanceMultiSelect &&
                        typeof performanceMultiSelect.appendTo === 'function'
                    ) {
                        performanceMultiSelect.appendTo(
                            '#performance-multiselect'
                        );
                        console.log(
                            '✅ Performance MultiSelect initialized successfully'
                        );
                    } else {
                        console.error(
                            '❌ Performance MultiSelect object is invalid'
                        );
                    }
                } catch (performanceError) {
                    console.error(
                        '❌ Error initializing Performance MultiSelect:',
                        performanceError
                    );
                }
            }
        } catch (error) {
            console.error(
                '❌ Error initializing MultiSelect components:',
                error
            );
        }
    }

    // ✅ SMART FILTER: Track date range changes for intelligent API calls
    let lastAppliedDateRange = null;

    // ✅ PERFORMANCE: Debounced apply filters to prevent rapid multiple calls
    const debouncedApplyFilters = debounce(function () {
        applyAllFiltersCore();
    }, 300); // 300ms debounce delay

    // ✅ Helper: Validate date range selection
    function validateDateRangeSelection() {
        if (
            dateRangePicker &&
            dateRangePicker.startDate &&
            dateRangePicker.endDate
        ) {
            const fromDate = new Date(dateRangePicker.startDate);
            const toDate = new Date(dateRangePicker.endDate);

            // Check if dates are valid
            if (isNaN(fromDate.getTime()) || isNaN(toDate.getTime())) {
                return false;
            }

            // Check if from date is not after to date
            if (fromDate > toDate) {
                return false;
            }

            return true;
        }
        return false;
    }

    // ✅ SMART FILTER: Apply filters with intelligent API call logic
    function applyAllFiltersCore() {
        if (
            !window.tiktokGmvMaxCampaignPivotTable ||
            !window.initialGmvMaxCampaignData
        )
            return;

        if (!validateDateRangeSelection()) {
            if (typeof abp !== 'undefined' && abp.notify) {
                abp.notify.warn(
                    'Vui lòng chọn khoảng thời gian trước khi áp dụng!'
                );
            } else {
                console.warn(
                    '⚠️ WARNING: Vui lòng chọn khoảng thời gian trước khi áp dụng!'
                );
            }
            return;
        }

        // ✅ SMART LOGIC: Check if date range has changed
        const currentDateRange = getCurrentDateRangeFromPicker();
        const dateRangeChanged = hasDateRangeChanged(
            currentDateRange,
            lastAppliedDateRange
        );

        if (dateRangeChanged) {
            // ✅ Date range changed → Call API to fetch new data
            callAPIWithNewDateRange(currentDateRange);
            lastAppliedDateRange = currentDateRange;
            return;
        } else {
            // ✅ Date range unchanged → Filter local data only
            filterLocalDataOnly();
        }
    }

    // ✅ Helper: Get current date range from DateRangePicker
    function getCurrentDateRangeFromPicker() {
        if (
            dateRangePicker &&
            dateRangePicker.startDate &&
            dateRangePicker.endDate
        ) {
            return {
                from: new Date(dateRangePicker.startDate),
                to: new Date(dateRangePicker.endDate),
            };
        }
        return null;
    }

    // ✅ Helper: Check if date range has changed
    function hasDateRangeChanged(current, last) {
        if (!current && !last) return false;
        if (!current || !last) return true;

        return (
            current.from.getTime() !== last.from.getTime() ||
            current.to.getTime() !== last.to.getTime()
        );
    }

    // ✅ Helper: Call API with new date range
    async function callAPIWithNewDateRange(dateRange) {
        try {
            showLoading('Đang tải dữ liệu mới...');

            // Build API parameters
            const params = new URLSearchParams({
                fromDate: dateRange.from.toISOString().split('T')[0],
                toDate: dateRange.to.toISOString().split('T')[0],
            });

            // Get currency from localStorage
            const currency =
                (typeof localStorage !== 'undefined'
                    ? localStorage.getItem('tiktok_currency')
                    : null) || 'USD';
            params.append('currency', currency);

            // Call API
            const response = await fetch(
                `/api/fact-gmv-max-campaign/data?${params}`
            );
            if (!response.ok) {
                throw new Error(`API call failed: ${response.status}`);
            }

            const newData = await response.json();

            // Update global data
            window.initialGmvMaxCampaignData = newData;

            // Apply all filters to new data
            filterLocalDataOnly();

            hideLoading();
            if (typeof abp !== 'undefined' && abp.notify) {
                abp.notify.success('Đã tải dữ liệu mới và áp dụng bộ lọc');
            } else {
                console.log('✅ SUCCESS: Đã tải dữ liệu mới và áp dụng bộ lọc');
            }
        } catch (error) {
            hideLoading();
            if (typeof abp !== 'undefined' && abp.notify) {
                abp.notify.error('Lỗi khi tải dữ liệu mới: ' + error.message);
            } else {
                console.error(
                    '❌ ERROR: Lỗi khi tải dữ liệu mới: ' + error.message
                );
            }
        }
    }

    // ✅ Helper: Filter local data only (existing logic)
    function filterLocalDataOnly() {
        let filteredData = { ...window.initialGmvMaxCampaignData };
        let filteredFacts = [
            ...window.initialGmvMaxCampaignData.factGmvMaxCampaigns,
        ];

        // Apply date range filter
        if (
            dateRangePicker &&
            dateRangePicker.startDate &&
            dateRangePicker.endDate
        ) {
            try {
                const fromDate = new Date(dateRangePicker.startDate);
                const toDate = new Date(dateRangePicker.endDate);

                if (!isNaN(fromDate.getTime()) && !isNaN(toDate.getTime())) {
                    filteredFacts = filteredFacts.filter((fact) => {
                        if (!fact.date) return true;
                        const factDate = new Date(fact.date);
                        if (isNaN(factDate.getTime())) return true;
                        return factDate >= fromDate && factDate <= toDate;
                    });
                }
            } catch (error) {
                // Handle error silently
            }
        }

        // Apply keyword search
        const keyword = $('#keyword-search').val().toLowerCase().trim();
        if (keyword) {
            filteredFacts = filteredFacts.filter((fact) => {
                const bcInfo =
                    window.initialGmvMaxCampaignData.dimBusinessCenters.find(
                        (bc) => bc.id === fact.dimBusinessCenterId
                    );
                const storeInfo =
                    window.initialGmvMaxCampaignData.dimStores.find(
                        (store) => store.id === fact.dimStoreId
                    );
                const campaignInfo =
                    window.initialGmvMaxCampaignData.dimCampaigns.find(
                        (campaign) => campaign.id === fact.dimCampaignId
                    );

                return (
                    (bcInfo?.bcName || '').toLowerCase().includes(keyword) ||
                    (storeInfo?.storeName || '')
                        .toLowerCase()
                        .includes(keyword) ||
                    (campaignInfo?.campaignName || '')
                        .toLowerCase()
                        .includes(keyword)
                );
            });
        }

        // Apply business center filter using Syncfusion MultiSelect
        const selectedBCs = businessCenterMultiSelect
            ? businessCenterMultiSelect.value || []
            : [];
        if (selectedBCs.length > 0) {
            filteredFacts = filteredFacts.filter((fact) =>
                selectedBCs.includes(fact.dimBusinessCenterId?.toString())
            );
        }

        // Apply shop filter using Syncfusion MultiSelect
        const selectedShops = shopMultiSelect
            ? shopMultiSelect.value || []
            : [];
        if (selectedShops.length > 0) {
            filteredFacts = filteredFacts.filter((fact) =>
                selectedShops.includes(fact.dimStoreId?.toString())
            );
        }

        // Apply campaign type filter using Syncfusion MultiSelect
        const selectedTypes = campaignTypeMultiSelect
            ? campaignTypeMultiSelect.value || []
            : [];
        if (selectedTypes.length > 0) {
            filteredFacts = filteredFacts.filter((fact) =>
                selectedTypes.includes(fact.shoppingAdsType)
            );
        }

        // Apply performance filter using Syncfusion MultiSelect
        const selectedPerformance = performanceMultiSelect
            ? performanceMultiSelect.value || []
            : [];
        if (selectedPerformance.length > 0) {
            filteredFacts = filteredFacts.filter((fact) => {
                const roas = parseFloat(fact.roas) || 0;
                return selectedPerformance.some((perf) => {
                    switch (perf) {
                        case 'excellent':
                            return roas > 3.0;
                        case 'good':
                            return roas >= 2.0 && roas <= 3.0;
                        case 'average':
                            return roas >= 1.5 && roas < 2.0;
                        case 'poor':
                            return roas < 1.5;
                        default:
                            return true;
                    }
                });
            });
        }

        filteredData.factGmvMaxCampaigns = filteredFacts;

        // ✅ SMART COLUMNS: Update columns based on current date range
        const smartColumns =
            window.tiktokGmvMaxCampaignPivotTable.getSmartTimeColumns();
        window.tiktokGmvMaxCampaignPivotTable.pivotTableObj.dataSourceSettings.columns =
            smartColumns;

        window.tiktokGmvMaxCampaignPivotTable.refreshData(filteredData);

        if (typeof abp !== 'undefined' && abp.notify) {
            abp.notify.success(
                `Đã áp dụng bộ lọc. Hiển thị ${filteredFacts.length} bản ghi.`
            );
        } else {
            console.log(
                `✅ SUCCESS: Đã áp dụng bộ lọc. Hiển thị ${filteredFacts.length} bản ghi.`
            );
        }
    }

    // Clear all filters
    async function clearAllFilters() {
        // Clear keyword search
        $('#keyword-search').val('');

        // Clear Date Range Picker and Quick Date DropDown
        if (dateRangePicker) {
            try {
                dateRangePicker.startDate = null;
                dateRangePicker.endDate = null;
                dateRangePicker.value = null;
                dateRangePicker.dataBind && dateRangePicker.dataBind();
            } catch (e) {
                // Handle error silently
            }
        }

        if (quickDateDropdown) {
            try {
                quickDateDropdown.value = null;
                quickDateDropdown.dataBind && quickDateDropdown.dataBind();
            } catch (e) {
                // Handle error silently
            }
        }

        // Clear Syncfusion MultiSelect components (use safe value reset instead of clear())
        if (businessCenterMultiSelect) {
            try {
                businessCenterMultiSelect.value = [];
                businessCenterMultiSelect.dataBind &&
                    businessCenterMultiSelect.dataBind();
                businessCenterMultiSelect.refresh &&
                    businessCenterMultiSelect.refresh();
            } catch (e) {
                // Handle error silently
            }
        }
        if (shopMultiSelect) {
            try {
                // Ensure dataSource exists before resetting value to avoid internal null.map errors
                if (
                    !shopMultiSelect.dataSource ||
                    (Array.isArray(shopMultiSelect.dataSource) &&
                        shopMultiSelect.dataSource.length === 0)
                ) {
                    shopMultiSelect.dataSource = availableShops || [];
                }
                shopMultiSelect.value = [];
                shopMultiSelect.dataBind && shopMultiSelect.dataBind();
                shopMultiSelect.refresh && shopMultiSelect.refresh();
            } catch (e) {
                // Handle error silently
            }
        }
        if (campaignTypeMultiSelect) {
            try {
                if (
                    !campaignTypeMultiSelect.dataSource ||
                    (Array.isArray(campaignTypeMultiSelect.dataSource) &&
                        campaignTypeMultiSelect.dataSource.length === 0)
                ) {
                    campaignTypeMultiSelect.dataSource = [
                        { value: 'PRODUCT', text: 'Sản phẩm' },
                        { value: 'LIVE', text: 'Live Stream' },
                    ];
                }
                campaignTypeMultiSelect.value = [];
                campaignTypeMultiSelect.dataBind &&
                    campaignTypeMultiSelect.dataBind();
                campaignTypeMultiSelect.refresh &&
                    campaignTypeMultiSelect.refresh();
            } catch (e) {
                // Handle error silently
            }
        }
        if (performanceMultiSelect) {
            try {
                performanceMultiSelect.value = [];
                performanceMultiSelect.dataBind &&
                    performanceMultiSelect.dataBind();
                performanceMultiSelect.refresh &&
                    performanceMultiSelect.refresh();
            } catch (e) {
                // Handle error silently
            }
        }

        // Ensure initial data is available
        if (
            !window.initialGmvMaxCampaignData &&
            window.loadCampaignDataForFilters
        ) {
            try {
                await window.loadCampaignDataForFilters();
            } catch (e) {
                // Handle error silently
            }
        }

        // Refresh pivot table with original data
        if (
            window.tiktokGmvMaxCampaignPivotTable &&
            window.initialGmvMaxCampaignData
        ) {
            await window.tiktokGmvMaxCampaignPivotTable.refreshData(
                window.initialGmvMaxCampaignData
            );
        }

        if (typeof abp !== 'undefined' && abp.notify) {
            abp.notify.success('Đã xóa tất cả bộ lọc');
        } else {
            console.log('✅ SUCCESS: Đã xóa tất cả bộ lọc');
        }
    }

    // Event listeners for new filter system
    $(document).ready(function () {
        // ✅ OPTIMIZED: Better Syncfusion loading detection with timeout
        function waitForSyncfusion() {
            if (
                typeof ej !== 'undefined' &&
                ej.calendars &&
                ej.calendars.DateRangePicker &&
                ej.dropdowns &&
                ej.dropdowns.DropDownList &&
                ej.dropdowns.MultiSelect
            ) {
                syncfusionReady = true;

                // ✅ OPTIMIZED: Only initialize if filters collapse is already open AND not already initialized
                const filtersCollapse =
                    document.getElementById('filtersCollapse');
                if (
                    filtersCollapse &&
                    filtersCollapse.classList.contains('show') &&
                    !filtersInitialized &&
                    !isInitializing
                ) {
                    setTimeout(async () => await populateFilterOptions(), 200); // ✅ INCREASED: From 100ms to 200ms
                }
            } else {
                setTimeout(waitForSyncfusion, 200); // ✅ INCREASED: From 100ms to 200ms
            }
        }

        // Start waiting for Syncfusion
        waitForSyncfusion();

        // ✅ FIXED: Backup mechanism - ensure Syncfusion is ready when needed
        let syncfusionCheckInterval = setInterval(() => {
            if (syncfusionReady) {
                clearInterval(syncfusionCheckInterval);
            }
        }, 200);

        // ✅ FIXED: Clear interval after 10 seconds to avoid infinite checking
        setTimeout(() => {
            clearInterval(syncfusionCheckInterval);
        }, 10000);

        // ✅ OPTIMIZED: Lazy initialize filters only when collapse is opened with better timing
        $('#filtersCollapse').on('show.bs.collapse', function () {
            if (!filtersInitialized && !isInitializing) {
                setTimeout(async () => {
                    try {
                        await populateFilterOptions();
                    } catch (error) {
                        console.error('❌ Error initializing filters:', error);
                    }
                }, 100); // ✅ INCREASED: From 50ms to 100ms for better stability
            }
        });

        // ✅ PERFORMANCE: Use debounced version for apply filters
        $('#apply-all-filters').click(debouncedApplyFilters);

        // Clear all filters button
        $('#clear-all-filters').click(function () {
            clearAllFilters();
        });

        // ✅ PERFORMANCE: Debounced keyword search for better performance
        const debouncedKeywordSearch = debounce(function () {
            const keyword = $('#keyword-search').val().trim();
            // Keyword search changed - user needs to click Apply
        }, 300);

        $('#keyword-search').on('input', debouncedKeywordSearch);

        // Note: Date filters are handled by Syncfusion components' change events

        // ✅ FIXED: Force initialize filters function
        window.forceInitializeFilters = async function () {
            if (syncfusionReady && !filtersInitialized) {
                await populateFilterOptions();
            }
        };

        // ✅ THÊM: Ranking tab event handlers
        $('#cost-ranking-tab').on('click', function () {
            // Show loading when switching to cost ranking tab
            RankingLoadingManager.showCostRankingLoading();

            // Simulate data loading (replace with actual data loading logic)
            setTimeout(() => {
                const rankingContent = `
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Xếp hạng</th>
                                    <th>Tên Shop</th>
                                    <th>Chi tiêu (USD)</th>
                                    <th>ROI</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>Shop A</td>
                                    <td>$1,000</td>
                                    <td>3.2</td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>Shop B</td>
                                    <td>$800</td>
                                    <td>2.8</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                `;
                RankingLoadingManager.hideCostRankingLoading(rankingContent);
            }, 1000);
        });

        $('#revenue-ranking-tab').on('click', function () {
            // Show loading when switching to revenue ranking tab
            RankingLoadingManager.showRevenueRankingLoading();

            // Simulate data loading (replace with actual data loading logic)
            setTimeout(() => {
                const rankingContent = `
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Xếp hạng</th>
                                    <th>Tên Shop</th>
                                    <th>Doanh thu (USD)</th>
                                    <th>Tăng trưởng</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>Shop C</td>
                                    <td>$5,000</td>
                                    <td>+15%</td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>Shop D</td>
                                    <td>$4,200</td>
                                    <td>+12%</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                `;
                RankingLoadingManager.hideRevenueRankingLoading(rankingContent);
            }, 1000);
        });
    });

    // ✅ FIXED: Quick date filter function with better error handling
    function setQuickDateFilter(period) {
        // ✅ FIXED: Validate period parameter
        if (!period || typeof period !== 'string') {
            console.warn(
                '⚠️ Invalid period parameter for setQuickDateFilter:',
                period
            );
            return;
        }

        try {
            const range =
                window.sharedDateHelper &&
                window.sharedDateHelper.getQuickDateRange
                    ? window.sharedDateHelper.getQuickDateRange(period)
                    : (function () {
                          const endDate = new Date();
                          let startDate = new Date();
                          if (period === 'today') {
                              // startDate already today
                          } else if (period === 'week') {
                              startDate.setDate(startDate.getDate() - 7);
                          } else if (period === 'month') {
                              startDate.setDate(startDate.getDate() - 30);
                          } else if (period === 'quarter') {
                              startDate.setDate(startDate.getDate() - 90);
                          } else {
                              startDate.setDate(startDate.getDate() - 7);
                          }
                          return { startDate: startDate, endDate: endDate };
                      })();

            // ✅ FIXED: Enhanced validation for date range
            if (!range || !range.startDate || !range.endDate) {
                console.warn('⚠️ Invalid date range from setQuickDateFilter');
                return;
            }

            if (
                isNaN(range.startDate.getTime()) ||
                isNaN(range.endDate.getTime())
            ) {
                console.warn('⚠️ Invalid date values in setQuickDateFilter');
                return;
            }

            if (dateRangePicker) {
                try {
                    // ✅ FIXED: Set startDate and endDate safely (no need to check if startDate is undefined)
                    dateRangePicker.startDate = range.startDate;
                    dateRangePicker.endDate = range.endDate;

                    // ✅ FIXED: Call dataBind if available to update the UI
                    if (typeof dateRangePicker.dataBind === 'function') {
                        dateRangePicker.dataBind();
                    }

                    console.log(
                        '✅ Quick date filter applied successfully:',
                        period
                    );
                } catch (dateError) {
                    console.error(
                        '❌ Error applying quick date filter:',
                        dateError
                    );
                }
            } else {
                console.warn(
                    '⚠️ DateRangePicker not available for quick date filter'
                );
            }
        } catch (error) {
            console.error('❌ Error in setQuickDateFilter:', error);
        }

        // Không auto-apply filters, chỉ cập nhật DateRangePicker
    }

    // ✅ Simple performance filter - like Level filter in FactBalance (keeping original for compatibility)
    $('input[name="performance-filter"]').change(function () {
        if (this.checked && window.tiktokGmvMaxCampaignPivotTable) {
            const filterValue = this.id.replace('performance-', '');

            if (filterValue !== 'all') {
                // Apply filter to pivot table data - simple approach
                let filteredData = window.initialGmvMaxCampaignData;
                if (filterValue === 'excellent') {
                    filteredData = {
                        ...window.initialGmvMaxCampaignData,
                        factGmvMaxCampaigns:
                            window.initialGmvMaxCampaignData.factGmvMaxCampaigns.filter(
                                (f) => (f.roas || 0) > 3.0
                            ),
                    };
                } else if (filterValue === 'good') {
                    filteredData = {
                        ...window.initialGmvMaxCampaignData,
                        factGmvMaxCampaigns:
                            window.initialGmvMaxCampaignData.factGmvMaxCampaigns.filter(
                                (f) => {
                                    const roas = f.roas || 0;
                                    return roas >= 2.0 && roas <= 3.0;
                                }
                            ),
                    };
                } else if (filterValue === 'poor') {
                    filteredData = {
                        ...window.initialGmvMaxCampaignData,
                        factGmvMaxCampaigns:
                            window.initialGmvMaxCampaignData.factGmvMaxCampaigns.filter(
                                (f) => (f.roas || 0) < 2.0
                            ),
                    };
                }

                window.tiktokGmvMaxCampaignPivotTable.refreshData(filteredData);
                if (typeof abp !== 'undefined' && abp.notify) {
                    abp.notify.success(
                        `Đã lọc theo ${this.nextElementSibling.textContent}`
                    );
                } else {
                    console.log(
                        `✅ SUCCESS: Đã lọc theo ${this.nextElementSibling.textContent}`
                    );
                }
            } else {
                window.tiktokGmvMaxCampaignPivotTable.refreshData(
                    window.initialGmvMaxCampaignData
                );
                if (typeof abp !== 'undefined' && abp.notify) {
                    abp.notify.success('Đã hiển thị tất cả chiến dịch');
                } else {
                    console.log('✅ SUCCESS: Đã hiển thị tất cả chiến dịch');
                }
            }
        }
    });

    // Use shared date parser instead of local implementation
    function parseDateToISO(dateStr) {
        if (window.sharedDateHelper && window.sharedDateHelper.parseDateToISO) {
            return window.sharedDateHelper.parseDateToISO(dateStr);
        }
        if (window.commonUtils && window.commonUtils.parseDateToISO) {
            return window.commonUtils.parseDateToISO(dateStr);
        }
        return null;
    }

    // ✅ Simple date filter - like FactBalance (just call API, no filter preservation)
    $('#date-filter-btn').click(function () {
        let fromDate = $('#from-date-filter').val();
        let toDate = $('#to-date-filter').val();
        if (!fromDate || !toDate) {
            if (typeof abp !== 'undefined' && abp.notify) {
                abp.notify.error('Vui lòng chọn cả ngày từ và ngày đến');
            } else {
                console.error('❌ ERROR: Vui lòng chọn cả ngày từ và ngày đến');
            }
            return;
        }

        // Normalize to ISO string for API
        fromDate = parseDateToISO(fromDate);
        toDate = parseDateToISO(toDate);

        if (!fromDate || !toDate) {
            if (typeof abp !== 'undefined' && abp.notify) {
                abp.notify.error('Định dạng ngày không hợp lệ');
            } else {
                console.error('❌ ERROR: Định dạng ngày không hợp lệ');
            }
            return;
        }

        // Simple API call like FactBalance - no filter preservation
        if (window.loadPivotTable) window.loadPivotTable(fromDate, toDate);
    });

    // ✅ NEW: Currency change handler for campaign dashboard
    $(document).on('currencyChanged', function (event, newCurrency) {
        // Refresh campaign dashboard with new currency
        if (window.refreshCampaignDashboard) {
            // Reset dashboard data loaded flag to force reload
            if (window.campaignDashboardDataLoaded !== undefined) {
                window.campaignDashboardDataLoaded = false;
            }
            window.refreshCampaignDashboard();
        }

        if (window.abp && window.abp.notify) {
            // Check if notifications are enabled (like FactGmvMaxProduct)
            if (
                !window.factGmvMaxCampaignNotifications ||
                window.factGmvMaxCampaignNotifications.currencyChange !== false
            ) {
                window.abp.notify.info(
                    `Đã chuyển đổi tiền tệ sang ${newCurrency} cho dashboard chiến dịch`
                );
            } else {
                console.info(
                    `✅ SUCCESS: Đã chuyển đổi tiền tệ sang ${newCurrency} cho dashboard chiến dịch`
                );
            }
        }

        // Also trigger pivot table reload if available
        if (window.loadPivotTable) {
            // Get current date range from date picker if available
            const fromDate = $('#fromDate').val();
            const toDate = $('#toDate').val();
            if (fromDate && toDate) {
                window.loadPivotTable(fromDate, toDate);
            }
        }
    });
});
