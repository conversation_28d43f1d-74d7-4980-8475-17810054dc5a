/**
 * ✅ COPY HOÀN TOÀN TiktokGmvMaxProductPivotTable từ factGmvMaxProduct.js
 * Chỉ thay đổi container ID và localStorage keys để tránh conflict
 */
class TiktokGmvMaxProductPivotTableForGmvMax {
    constructor(containerId) {
        this.containerId = containerId;
        this.pivotTableObj = null;
        this.currencyManager = new Currency('product');
        this.currentCurrency = this.currencyManager.getCurrentCurrency();
        this.availableCurrencies = ['USD', 'VND'];

        this.pendingRefresh = false;
        this.refreshTimeout = null;

        this.cachedLookups = null;
        this.lastDataHash = null;

        this.performanceMetrics = {
            refreshCount: 0,
            dataProcessingTime: 0,
            lastRefreshTime: null,
            totalProcessedRecords: 0,
        };

        this.dataBoundTimeout = null;

        // Debounce timer for insights updates
        this._insightsTimer = null;
        this._insightsDebounceMs = 500;
        // Debounce timer for conditional formatting
        this._formattingTimer = null;
        this._formattingDebounceMs = 300;

        // Get alert thresholds from window.ProductAlertThresholds
        this.alertThresholds = window.ProductAlertThresholds || {
            roasCritical: 1.5, // ROAS < 1.5
            roasLow: 2.0, // ROAS < 2.0
            roasGood: 3.0, // ROAS > 3.0
            tacosHigh: 30, // TACOS > 30%
            tacosMedium: 20, // TACOS > 20%
            lowSalesThreshold: 5, // Less than 5 units sold
            highCostPerOrderThreshold: 100, // Cost per order > $100
        };

        // ✅ Heatmap thresholds for product performance
        this.heatmapThresholds = {
            // ROAS thresholds
            roasEmergency: 1.0, // Dark red - product losing money
            roasCritical: this.alertThresholds.roasCritical, // Red - critical ROAS
            roasLow: this.alertThresholds.roasLow, // Orange - low ROAS
            roasGood: this.alertThresholds.roasGood, // Green - good ROAS
            roasExcellent: 5.0, // Dark green - excellent ROAS
            // Colors for heatmap visualization
            colors: {
                emergency: { bg: '#d32f2f', color: '#ffffff' }, // Dark red
                critical: { bg: '#f44336', color: '#ffffff' }, // Red
                low: { bg: '#ff9800', color: '#ffffff' }, // Orange
                warning: { bg: '#ffc107', color: '#212121' }, // Yellow
                good: { bg: '#4caf50', color: '#ffffff' }, // Green
                excellent: { bg: '#2e7d32', color: '#ffffff' }, // Dark green
                veryHigh: { bg: '#1b5e20', color: '#ffffff' }, // Very dark green
            },
        };
    }

    // ✅ COPY HOÀN TOÀN method initial() từ factGmvMaxProduct.js
    async initial() {
        const dataSource = await this.extractPivotDataOptimized(['product']);

        this.fullDataSource = dataSource;

        // Get filtered values based on permissions
        const filteredValues = await this.getCurrencyValuesWithAggregations();

        this.pivotTableObj = new ej.pivotview.PivotView({
            dataSourceSettings: {
                dataSource: dataSource,
                allowLabelFilter: false,
                allowValueFilter: false,
                allowMemberFilter: false,
                enableSorting: true,
                allowCalculatedField: true,

                // ✅ New row hierarchy: BC => Shop => Campaign => Tên kênh => Product
                rows: [
                    {
                        name: 'BusinessCenterName',
                        caption: 'Trung tâm kinh doanh',
                        showSubTotals: true,
                    },
                    {
                        name: 'StoreName',
                        caption: 'Tên Shop',
                        showSubTotals: true,
                    },
                    {
                        name: 'CampaignName',
                        caption: 'Campaign',
                        showSubTotals: true,
                    },
                    {
                        name: 'TtAccountName',
                        caption: 'Tên kênh',
                        showSubTotals: true,
                    },
                    {
                        name: 'ProductName',
                        caption: 'Tên sản phẩm',
                        showSubTotals: false,
                    },
                ],

                columns: this.getSmartTimeColumns(),

                // ✅ Product metrics with dynamic currency and per-column aggregation
                values: filteredValues,

                // ✅ Filters
                filters: [],

                // ✅ Number formatting with currency support
                formatSettings: this.getCurrencyFormatSettings(),

                // ✅ Calculated fields for product analysis
                calculatedFieldSettings: [
                    {
                        name: 'Revenue_Per_Unit',
                        formula:
                            '"Orders" > 0 ? ("GrossRevenue" / "Orders") : 0',
                        caption: 'Doanh thu mỗi sản phẩm',
                    },
                    {
                        name: 'Product_Efficiency_Score',
                        formula:
                            '("ROAS" > 3 && "Orders" > 20) ? 100 : (("ROAS" > 2 && "Orders" > 5) ? 70 : (("ROAS" > 1.5) ? 40 : 20))',
                        caption: 'Điểm hiệu quả sản phẩm',
                    },
                ],

                expandAll: true,

                // ✅ Hide unwanted values by default - USD only
                excludeFields: [
                    'NetCost',
                    'NetCostVND', // Legacy VND fields
                    'CostPerOrderVND', // Legacy VND fields
                    'CPMVND', // Legacy VND fields
                    'CPCVND', // Legacy VND fields
                    'ProductPriceVND', // Legacy VND fields
                    // Note: Keep USD fields available for calculations
                ],

                // ✅ Sort by performance metrics
                sortSettings: [
                    { name: 'ROAS', order: 'Descending' },
                    { name: 'Orders', order: 'Descending' },
                ],
            },

            locale: 'vi-VN',
            height: 1000,
            width: '100%',
            showGroupingBar: false,
            showFieldList: true,
            allowExcelExport: true,
            allowPdfExport: false,
            showToolbar: true,

            showValuesButton: false,
            showRowSubTotals: false,
            showColumnSubTotals: false,
            showGrandTotals: false,
            gridSettings: {
                layout: 'Tabular',
                columnWidth: 140,
                allowSelection: false,
                selectionSettings: { mode: 'Cell', type: 'Multiple' },
                // ✅ Performance optimization: Enable virtualization if supported
                enableVirtualization: true,
                allowPaging: true,
                pageSize: 100,
            },

            // ✅ Toolbar for product analysis
            toolbar: [
                'SubTotal',
                'GrandTotal',
                'FieldList',
                'ConditionalFormatting',
                'NumberFormatting',
            ],

            // ✅ Product performance heatmap (disabled by default for performance)
            conditionalFormatSettings: [],
            allowConditionalFormatting: true,

            // ✅ Event handlers
            cellClick: (args) => {
                this.handleCellClick(args);
            },

            dataBound: () => {
                if (this.dataBoundTimeout) {
                    clearTimeout(this.dataBoundTimeout);
                }
                this.dataBoundTimeout = setTimeout(() => {
                    this.updateProductInsights();
                }, 200); // 200ms debounce
            },

            // Chart settings for product analysis
            chartSettings: {
                chartSeries: {
                    type: 'Column',
                    animation: { enable: true },
                },
                primaryYAxis: {
                    title: 'Doanh thu (USD)',
                    labelFormat: 'C0',
                },
            },
        });

        // Render to container
        this.pivotTableObj.appendTo(`#${this.containerId}`);

        await this.applyInitialValuesConfiguration();
    }

    // ✅ COPY HOÀN TOÀN từ factGmvMaxProduct.js
    async applyInitialValuesConfiguration() {
        // Get saved configuration from localStorage - shared with FactGmvMaxProduct
        let savedConfig = localStorage.getItem('productColumnAggregations');
        let desiredValues;

        if (savedConfig) {
            try {
                const config = JSON.parse(savedConfig);
                // Support new format with selectedFields and aggregations
                if (config.selectedFields && config.aggregations) {
                    desiredValues = config.selectedFields;
                } else {
                    // Old format - treat as aggregations only, use defaults
                    desiredValues = [
                        'GrossRevenue',
                        'Cost',
                        'CostPerOrder',
                        'ROAS',
                        'Orders',
                    ];
                }
            } catch (e) {
                console.warn(
                    '⚠️ Error parsing saved configuration, using defaults:',
                    e
                );
                desiredValues = [
                    'GrossRevenue',
                    'Cost',
                    'CostPerOrder',
                    'ROAS',
                    'Orders',
                ];
            }
        } else {
            desiredValues = [
                'GrossRevenue',
                'Cost',
                'CostPerOrder',
                'ROAS',
                'Orders',
            ];
        }

        // Apply values directly to pivot table configuration without triggering refresh
        if (desiredValues && desiredValues.length > 0) {
            const currentAggregations = this.getStoredColumnAggregations();
            const currencyValues = await this.getCurrencyValues(
                currentAggregations
            );
            const newValues = currencyValues.filter((value) =>
                desiredValues.includes(value.name)
            );

            this.pivotTableObj.dataSourceSettings.values = newValues;
        } else {
            console.warn('⚠️ No valid values to apply, keeping default values');
        }
    }

    // ✅ COPY HOÀN TOÀN extractPivotDataOptimized từ factGmvMaxProduct.js
    async extractPivotDataOptimized(includes = ['product'], dataToUse = null) {
        let sourceData = dataToUse;

        if (!sourceData) {
            // ✅ Sử dụng data từ global scope như màn hình hiện có
            sourceData = window.initialGmvMaxProductDataForGmvMax;
        }

        if (!sourceData?.factGmvMaxProducts) {
            console.warn('No product data found, using empty dataset');
            return [];
        }

        const facts = sourceData.factGmvMaxProducts;
        if (!facts || facts.length === 0) {
            console.warn('FactGmvMaxProducts array is empty');
            return [];
        }

        const transformedData = [];
        const BATCH_SIZE = 500;
        const lookups = this.createCachedLookups(sourceData);
        const processingStart = performance.now();

        for (let i = 0; i < facts.length; i += BATCH_SIZE) {
            const batch = facts.slice(i, i + BATCH_SIZE);

            const batchResults = batch.map((fact) => {
                const dateInfo = lookups.date[fact.dimDateId] || {};
                const adAccountInfo =
                    lookups.adAccount[fact.dimAdAccountId] || {};
                const businessCenterInfo =
                    lookups.businessCenter[fact.dimBusinessCenterId] || {};
                const campaignInfo = lookups.campaign[fact.dimCampaignId] || {};
                const storeInfo = lookups.store[fact.dimStoreId] || {};
                const productInfo = fact.dimProductId
                    ? lookups.product[fact.dimProductId]
                    : null;

                const transformedRecord = {
                    // Core product identification
                    ProductId: fact.productId,
                    ProductName:
                        fact.productName ||
                        productInfo?.productName ||
                        fact.productId,
                    SKU: fact.sku || productInfo?.sku || '',
                    Category:
                        fact.category ||
                        productInfo?.category ||
                        'Uncategorized',

                    // Product pricing info
                    ProductPrice: this.getCurrencyValue(fact, 'productPrice'),

                    // Business context
                    BusinessCenterName:
                        businessCenterInfo.bcName || 'Unknown BC',
                    BusinessCenterId: businessCenterInfo.bcId || '',
                    AdAccountName: adAccountInfo.advertiserName || '',
                    AdAccountId: adAccountInfo.advertiserId || '',
                    TtAccountName: adAccountInfo.ttAccountName || '',

                    // Campaign and Store info
                    CampaignName: campaignInfo.campaignName || '',
                    CampaignId: fact.campaignId,
                    StoreName: storeInfo.storeName || 'Unknown Store',
                    StoreId: fact.storeId,

                    // Date context
                    Date: dateInfo.fullDate || fact.date,
                    DateFormatted:
                        dateInfo.dateFormat_DDMMYYYY || formatDate(fact.date),
                    DateKey: fact.dimDateId,
                    Year: dateInfo.year || new Date(fact.date).getFullYear(),
                    Month: dateInfo.month || new Date(fact.date).getMonth() + 1,
                    MonthName: dateInfo.monthName || getMonthName(fact.date),
                    WeekDay: getVietnameseWeekday(new Date(fact.date)),
                    WeekOfYear: getWeekOfYear(fact.date),
                    WeekOfMonth: getWeekOfMonth(fact.date),
                    WeekStartDate: getWeekStartDate(fact.date),
                    WeekEndDate: getWeekEndDate(fact.date),
                    Quarter: getQuarter(fact.date),

                    // ✅ New formatted fields for smart grouping
                    WeekMonthYear: getWeekMonthYear(fact.date),
                    MonthYear: getMonthYear(fact.date),
                    YearFormatted: getYearFormatted(fact.date),

                    // Financial metrics with currency support
                    Cost: this.getCurrencyValue(fact, 'cost'),
                    NetCost: this.getCurrencyValue(fact, 'netCost'),
                    GrossRevenue: this.getCurrencyValue(fact, 'grossRevenue'),
                    AdsRevenue: this.getCurrencyValue(fact, 'adsRevenue'),
                    OrganicRevenue: this.getCurrencyValue(
                        fact,
                        'organicRevenue'
                    ),

                    // Performance metrics
                    Orders: fact.orders || 0,
                    QuantitySold: fact.quantitySold || 0,
                    CostPerOrder: this.getCurrencyValue(fact, 'costPerOrder'),
                    ROI: fact.roi || 0,
                    ROAS: fact.roas || 0,
                    ACOS: fact.acos || 0,
                    TACOS: fact.tacos || 0,

                    // Traffic metrics
                    Impressions: fact.impressions || 0,
                    Clicks: fact.clicks || 0,
                    CTR: fact.ctr || 0,
                    CPM: this.getCurrencyValue(fact, 'cpm'),
                    CPC: this.getCurrencyValue(fact, 'cpc'),
                    ConversionRate: fact.conversionRate || 0,

                    Currency: fact.currency || 'USD',
                };

                this.calculateProductMetricsInline(transformedRecord);
                this.addProductClassificationsInline(transformedRecord);

                return transformedRecord;
            });

            transformedData.push(...batchResults);

            if (i + BATCH_SIZE < facts.length) {
                await new Promise((resolve) => setTimeout(resolve, 0));
            }
        }

        // ✅ PERFORMANCE: Store processing metrics
        const processingDuration = performance.now() - processingStart;
        this.performanceMetrics.dataProcessingTime = processingDuration;
        this.performanceMetrics.totalProcessedRecords = transformedData.length;

        return transformedData;
    }

    // ✅ COPY tất cả helper methods từ factGmvMaxProduct.js
    batchedRefresh(changes = {}) {
        // Clear any pending refresh
        if (this.refreshTimeout) {
            clearTimeout(this.refreshTimeout);
        }

        // Apply changes immediately but defer refresh
        if (changes.values && this.pivotTableObj) {
            this.pivotTableObj.dataSourceSettings.values = changes.values;
        }
        if (changes.dataSource && this.pivotTableObj) {
            this.pivotTableObj.dataSourceSettings.dataSource =
                changes.dataSource;
        }
        if (changes.formatSettings && this.pivotTableObj) {
            this.pivotTableObj.dataSourceSettings.formatSettings =
                changes.formatSettings;
        }
        if (changes.conditionalFormatSettings && this.pivotTableObj) {
            this.pivotTableObj.dataSourceSettings.conditionalFormatSettings =
                changes.conditionalFormatSettings;
        }
        if (changes.filterSettings && this.pivotTableObj) {
            this.pivotTableObj.dataSourceSettings.filterSettings =
                changes.filterSettings;
        }

        // Batch refresh with small delay to catch multiple rapid changes
        this.refreshTimeout = setTimeout(() => {
            if (this.pivotTableObj && !this.pendingRefresh) {
                this.pendingRefresh = true;

                const refreshStart = performance.now();
                this.performanceMetrics.refreshCount++;
                this.performanceMetrics.lastRefreshTime = Date.now();

                this.pivotTableObj.refresh();

                // Reset flag after refresh completes
                setTimeout(() => {
                    this.pendingRefresh = false;
                }, 100);
            }
        }, 50); // 50ms delay to batch multiple rapid changes
    }

    generateDataHash(data) {
        if (!data) return null;

        const factCount = data.factGmvMaxProducts?.length || 0;
        const dimCounts = [
            data.dimAdAccounts?.length || 0,
            data.dimBusinessCenters?.length || 0,
            data.dimCampaigns?.length || 0,
            data.dimStores?.length || 0,
            data.dimProducts?.length || 0,
            data.dimDates?.length || 0,
        ].join('-');

        return `${factCount}_${dimCounts}`;
    }

    createCachedLookups(data) {
        if (!data) return {};

        const dataHash = this.generateDataHash(data);

        // Return cached lookups if data hasn't changed
        if (this.lastDataHash === dataHash && this.cachedLookups) {
            return this.cachedLookups;
        }
        this.cachedLookups = {
            adAccount: this.createLookup(data.dimAdAccounts, 'id'),
            businessCenter: this.createLookup(data.dimBusinessCenters, 'id'),
            campaign: this.createLookup(data.dimCampaigns, 'id'),
            store: this.createLookup(data.dimStores, 'id'),
            product: this.createLookup(data.dimProducts, 'id'),
            date: this.createLookup(data.dimDates, 'id'),
        };

        this.lastDataHash = dataHash;
        return this.cachedLookups;
    }

    // ✅ Helper methods
    createLookup(array, keyField) {
        if (!array) return {};
        const lookup = {};
        array.forEach((item) => {
            lookup[item[keyField]] = item;
        });
        return lookup;
    }

    // ✅ COPY methods từ factGmvMaxProduct.js
    getCurrencyValue(fact, fieldName) {
        if (this.currentCurrency === 'VND') {
            const vndVal = fact[`${fieldName}VND`];
            if (vndVal !== undefined) return vndVal || 0;
        } else {
            const usdVal = fact[`${fieldName}USD`];
            if (usdVal !== undefined) return usdVal || 0;
        }
        const fallback = fact[fieldName];
        return fallback || 0;
    }

    getCurrencyFormatSettings() {
        const isVnd = this.currentCurrency === 'VND';
        const suffix = isVnd ? ' đ' : ' $';
        return [
            { name: 'Cost', format: 'N0', suffix },
            { name: 'GrossRevenue', format: 'N0', suffix },
            { name: 'AdsRevenue', format: 'N0', suffix },
            { name: 'ProductPrice', format: 'N2', suffix },
            { name: 'CostPerOrder', format: 'N2', suffix },
            { name: 'ROAS', format: 'N2', suffix: 'x' },
            { name: 'TACOS', format: 'P1' },
            { name: 'ConversionRate', format: 'N2', suffix: '%' },
        ];
    }

    async getCurrencyValues(columnAggregations = null) {
        // ✅ NEW: Dynamic currency support like FactBalance
        const currency = this.currentCurrency;
        const currencySymbol = currency === 'VND' ? '₫' : '$';
        const aggregations =
            columnAggregations || this.getStoredColumnAggregations();

        // Get unified FactGmvMax permissions - wait for ABP to be ready
        await window.PermissionHelper.waitForABP();
        const permissions = window.PermissionHelper.getPermissions('product');

        const allValues = [
            // Revenue Metrics
            {
                name: 'GrossRevenue',
                caption: `Tổng doanh thu (${currencySymbol})`,
                type: aggregations['GrossRevenue'] || 'Sum',
                showSubTotals: true,
                category: 'metrics',
            },
            {
                name: 'AdsRevenue',
                caption: `Doanh thu quảng cáo (${currencySymbol})`,
                type: aggregations['AdsRevenue'] || 'Sum',
                showSubTotals: true,
                category: 'metrics',
            },
            {
                name: 'OrganicRevenue',
                caption: `Doanh thu tự nhiên (${currencySymbol})`,
                type: aggregations['OrganicRevenue'] || 'Sum',
                showSubTotals: true,
                category: 'metrics',
            },

            // Cost Metrics
            {
                name: 'Cost',
                caption: `Chi phí quảng cáo (${currencySymbol})`,
                type: aggregations['Cost'] || 'Sum',
                showSubTotals: true,
                category: 'spending',
            },
            {
                name: 'CostPerOrder',
                caption: `Chi phí mỗi đơn (${currencySymbol})`,
                type: aggregations['CostPerOrder'] || 'Avg',
                showSubTotals: false,
                category: 'spending',
            },

            // Product Specific Metrics
            {
                name: 'ProductPrice',
                caption: `Giá sản phẩm (${currencySymbol})`,
                type: aggregations['ProductPrice'] || 'Avg',
                showSubTotals: false,
                category: 'metrics',
            },
            {
                name: 'QuantitySold',
                caption: `Số lượng bán được`,
                type: aggregations['QuantitySold'] || 'Sum',
                showSubTotals: true,
                category: 'metrics',
            },

            // Performance Metrics
            {
                name: 'ROAS',
                caption: 'ROI (Tỷ lệ hoàn vốn quảng cáo)',
                type: aggregations['ROAS'] || 'Avg',
                showSubTotals: true,
                category: 'metrics',
            },
            {
                name: 'TACOS',
                caption: 'TACOS (Tỷ lệ chi phí quảng cáo trên doanh thu)',
                type: aggregations['TACOS'] || 'Avg',
                showSubTotals: true,
                category: 'restricted',
            },

            // Volume/Traffic Metrics
            {
                name: 'Orders',
                caption: 'Số đơn hàng',
                type: aggregations['Orders'] || 'Sum',
                showSubTotals: true,
                category: 'metrics',
            },
            {
                name: 'Impressions',
                caption: 'Lượt xem sản phẩm',
                type: aggregations['Impressions'] || 'Sum',
                showSubTotals: false,
                category: 'metrics',
            },
            {
                name: 'Clicks',
                caption: 'Lượt click sản phẩm',
                type: aggregations['Clicks'] || 'Sum',
                showSubTotals: false,
                category: 'metrics',
            },
        ];

        // Filter values based on permissions
        return allValues.filter((value) => {
            if (permissions.viewAll || permissions.viewAllAdvertisers)
                return true;
            if (
                value.category === 'spending' &&
                (permissions.viewSpending || permissions.viewAllAdvertisers)
            )
                return true;
            if (
                value.category === 'metrics' &&
                (permissions.viewMetrics || permissions.viewAllAdvertisers)
            )
                return true;
            if (
                value.category === 'restricted' &&
                (permissions.viewAll || permissions.viewAllAdvertisers)
            )
                return true;
            return false;
        });
    }

    // ✅ COPY tất cả các methods còn lại từ factGmvMaxProduct.js
    async getCurrencyValuesWithAggregations() {
        const currentAggregations = this.getStoredColumnAggregations();
        return await this.getCurrencyValues(currentAggregations);
    }

    getStoredColumnAggregations() {
        const stored = localStorage.getItem('productColumnAggregations');
        if (stored) {
            try {
                return JSON.parse(stored);
            } catch (e) {
                console.warn(
                    'Failed to parse stored column aggregations, using defaults'
                );
            }
        }

        // Return default aggregations for products
        return {
            Cost: 'Sum',
            GrossRevenue: 'Sum',
            Orders: 'Sum',
            ROAS: 'Avg',
            ACOS: 'Avg',
            Impressions: 'Sum',
            Clicks: 'Sum',
            CTR: 'Avg',
            CostPerOrder: 'Avg',
            ROI: 'Avg',
            AdsRevenue: 'Sum',
            OrganicRevenue: 'Sum',
            ProductPrice: 'Avg',
            QuantitySold: 'Sum',
            TACOS: 'Avg',
            ConversionRate: 'Avg',
        };
    }

    storeColumnAggregations(aggregations) {
        localStorage.setItem(
            'productColumnAggregations',
            JSON.stringify(aggregations)
        );
    }

    // ✅ COPY methods từ factGmvMaxProduct.js
    calculateProductMetricsInline(record) {
        // ✅ Single-pass calculation for better performance
        const clicks = record.Clicks || 0;
        const impressions = record.Impressions || 0;
        const grossRevenue = record.GrossRevenue || 0;
        const cost = record.Cost || 0;
        const orders = record.Orders || 0;
        const quantitySold = record.QuantitySold || 0;

        // Conversion rate
        record.ConversionRate =
            clicks > 0 ? parseFloat(((orders / clicks) * 100).toFixed(2)) : 0;

        // Revenue per unit
        record.RevenuePerUnit =
            quantitySold > 0
                ? parseFloat((grossRevenue / quantitySold).toFixed(2))
                : 0;

        // Cost per unit
        record.CostPerUnit =
            quantitySold > 0 ? parseFloat((cost / quantitySold).toFixed(2)) : 0;

        // Profit margin per unit
        record.ProfitMarginPerUnit =
            record.RevenuePerUnit > 0
                ? parseFloat(
                      (
                          ((record.RevenuePerUnit - record.CostPerUnit) /
                              record.RevenuePerUnit) *
                          100
                      ).toFixed(2)
                  )
                : 0;
    }

    addProductClassificationsInline(record) {
        const roas = record.ROAS || 0;
        const tacosPct = (record.TACOS || 0) * 100;
        const quantitySold = record.QuantitySold || 0;

        // ✅ Inline calculations for better performance
        record.ROASStatus =
            roas >= this.alertThresholds.roasGood
                ? 'Excellent'
                : roas >= this.alertThresholds.roasLow
                ? 'Good'
                : roas >= this.alertThresholds.roasCritical
                ? 'Warning'
                : 'Critical';

        record.ACOSStatus =
            tacosPct >= this.alertThresholds.tacosHigh
                ? 'High'
                : tacosPct >= this.alertThresholds.tacosMedium
                ? 'Medium'
                : 'Low';

        record.SalesStatus =
            quantitySold >= 50
                ? 'Hot Seller'
                : quantitySold >= 10
                ? 'Regular'
                : quantitySold >= this.alertThresholds.lowSalesThreshold
                ? 'Slow'
                : 'Very Slow';

        record.ProductHealth =
            roas >= 3.0 && tacosPct <= 30 && quantitySold >= 50
                ? 'Excellent'
                : roas >= 2.0 && tacosPct <= 40 && quantitySold >= 10
                ? 'Good'
                : roas >= 1.5 && quantitySold >= 5
                ? 'Fair'
                : 'Poor';

        record.AlertLevel =
            roas < 1.5 ||
            tacosPct > this.alertThresholds.tacosHigh ||
            quantitySold < this.alertThresholds.lowSalesThreshold
                ? 'High'
                : roas < 2.0 ||
                  tacosPct > this.alertThresholds.tacosMedium ||
                  quantitySold < 10
                ? 'Medium'
                : 'Low';
    }

    getSmartTimeColumns() {
        try {
            // Prefer date range from page-level config or picker instances
            let from = null;
            let to = null;

            // Try Syncfusion DateRangePicker instance on Campaign tab
            const pickerEl = document.getElementById(
                'campaign-date-range-picker'
            );
            if (
                pickerEl &&
                pickerEl.ej2_instances &&
                pickerEl.ej2_instances[0]
            ) {
                const picker = pickerEl.ej2_instances[0];
                from = picker.startDate ? new Date(picker.startDate) : null;
                to = picker.endDate ? new Date(picker.endDate) : null;
            }

            // Fallback: page config embedded in DOM
            if (
                (!from || !to) &&
                window.gmvMaxConfig &&
                window.gmvMaxConfig.dateRange
            ) {
                from = new Date(window.gmvMaxConfig.dateRange.from);
                to = new Date(window.gmvMaxConfig.dateRange.to);
            }

            if (from && to) {
                return getSmartTimeGrouping({ from, to });
            }
            return [{ name: 'DateFormatted', caption: 'Ngày-Tháng-Năm' }];
        } catch (error) {
            console.error('Error in getSmartTimeColumns:', error);
            return [{ name: 'DateFormatted', caption: 'Ngày-Tháng-Năm' }];
        }
    }

    handleCellClick(args) {
        if (args.currentCell && args.data) {
            const cellData = args.data[0];
            if (!cellData) return;

            // Show alerts for poor performing products
            if (
                cellData.ProductHealth === 'Poor' ||
                cellData.SalesStatus === 'Very Slow'
            ) {
                this.showProductAlert(cellData);
            }
        }
    }

    showProductAlert(data) {
        const message = `
            <strong>⚠️ Cảnh báo hiệu suất sản phẩm!</strong><br>
            Sản phẩm: ${data.ProductName}<br>
            ROAS: ${data.ROAS}x (${data.ROASStatus})<br>
            ACOS: ${data.ACOS}% (${data.ACOSStatus})<br>
            Số lượng bán: ${data.QuantitySold} (${data.SalesStatus})<br>
            <em>Cần tối ưu hóa hoặc dừng quảng cáo!</em>
        `;
        showToast('warning', message);
    }

    updateProductInsights() {
        // Dashboard is now independent of pivot table, no need to regenerate
        // Only update pivot table specific insights if needed
    }

    async refreshData(newData) {
        if (this.pivotTableObj && newData) {
            // ✅ Don't overwrite original data when filtering - just refresh display
            this.pivotTableObj.dataSourceSettings.dataSource =
                await this.extractPivotDataOptimized(['product'], newData);
            this.pivotTableObj.refresh();
        }
    }

    exportToExcel(fileName = 'TikTok_GMV_Max_Product_Analysis') {
        if (this.pivotTableObj) {
            this.pivotTableObj.excelExport({
                fileName: `${fileName}_${new Date()
                    .toISOString()
                    .slice(0, 10)}.xlsx`,
                includeHeader: true,
            });
        }
    }

    exportToPdf(fileName = 'TikTok_GMV_Max_Product_Report') {
        if (this.pivotTableObj) {
            this.pivotTableObj.pdfExport({
                fileName: `${fileName}_${new Date()
                    .toISOString()
                    .slice(0, 10)}.pdf`,
                includeHeader: true,
            });
        }
    }

    showChart() {
        if (this.pivotTableObj) {
            this.pivotTableObj.displayOption.view = 'Chart';
            this.pivotTableObj.chartSettings.chartSeries.type = 'Column';
            this.pivotTableObj.refresh();
        }
    }

    showGrid() {
        if (this.pivotTableObj) {
            this.pivotTableObj.displayOption.view = 'Grid';
            this.pivotTableObj.refresh();
        }
    }

    // ✅ Update pivot table with column aggregations
    async updatePivotTableWithColumnAggregations() {
        if (!this.pivotTableObj) return;

        try {
            const currentAggregations = this.getStoredColumnAggregations();
            // Get configuration from merged localStorage structure
            const config = JSON.parse(
                localStorage.getItem('productColumnAggregations') || '{}'
            );
            const selectedValues = config.selectedFields || [
                'GrossRevenue',
                'Cost',
                'CostPerOrder',
                'ROAS',
                'Orders',
            ];

            if (selectedValues.length === 0) {
                return;
            }

            // Create new value definitions with current column aggregations
            const allValues = await this.getCurrencyValues(currentAggregations);
            const newValues = allValues.filter((v) =>
                selectedValues.includes(v.name)
            );

            // Update pivot table values
            this.pivotTableObj.dataSourceSettings.values = newValues;

            // Refresh pivot table
            this.pivotTableObj.refresh();
        } catch (error) {
            console.error(
                'Failed to update pivot table with aggregations:',
                error
            );
        }
    }
}
