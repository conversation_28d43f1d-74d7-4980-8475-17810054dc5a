using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTok.DataSync
{
    /// <summary>
    /// Helper class cho việc xử lý đồng bộ tài khoản quảng cáo
    /// </summary>
    public static class AdAccountSyncHelper
    {
        /// <summary>
        /// Parse danh sách advertiser IDs không có quyền từ message lỗi
        /// </summary>
        /// <param name="message">Message lỗi từ API</param>
        /// <returns>Danh sách advertiser IDs không có quyền</returns>
        public static List<string> ParseUnauthorizedAdvertiserIds(string? message)
        {
            if (string.IsNullOrEmpty(message))
                return new List<string>();

            // Pattern để tìm các advertiser IDs trong message
            // Ví dụ: "No permission to operate advertiser: 7525383853067059201,7356899063901650945,..."
            var pattern = @"No permission to operate advertiser:\s*([\d,]+)";
            var match = Regex.Match(message, pattern);

            if (match.Success && match.Groups.Count > 1)
            {
                var advertiserIdsString = match.Groups[1].Value;
                return advertiserIdsString.Split(',')
                    .Select(id => id.Trim())
                    .Where(id => !string.IsNullOrEmpty(id))
                    .ToList();
            }

            return new List<string>();
        }


        /// <summary>
        /// Kiểm tra xem response có phải là lỗi quyền truy cập không
        /// </summary>
        /// <typeparam name="T">Kiểu dữ liệu của response data</typeparam>
        /// <param name="response">Response từ API</param>
        /// <returns>True nếu là lỗi quyền truy cập</returns>
        public static bool IsPermissionError<T>(TikTokApiResponse<T> response)
        {
            return response.Code == TikTok.Consts.TikTokApiCodes.NoPermission;
        }

        /// <summary>
        /// Lọc danh sách advertiser IDs có quyền truy cập
        /// </summary>
        /// <param name="allAdvertiserIds">Tất cả advertiser IDs</param>
        /// <param name="unauthorizedIds">Danh sách IDs không có quyền</param>
        /// <returns>Danh sách advertiser IDs có quyền</returns>
        public static List<string> GetAuthorizedAdvertiserIds(List<string> allAdvertiserIds, List<string> unauthorizedIds)
        {
            return allAdvertiserIds.Except(unauthorizedIds).ToList();
        }

        /// <summary>
        /// Tạo log message cho việc xử lý lỗi quyền truy cập
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="unauthorizedCount">Số lượng tài khoản không có quyền</param>
        /// <param name="authorizedCount">Số lượng tài khoản có quyền</param>
        /// <param name="unauthorizedIds">Danh sách IDs không có quyền</param>
        /// <param name="authorizedIds">Danh sách IDs có quyền</param>
        /// <returns>Log message</returns>
        public static string CreatePermissionErrorLogMessage(string bcId, int unauthorizedCount, int authorizedCount,
            List<string> unauthorizedIds, List<string> authorizedIds)
        {
            var message = $"Xử lý lỗi quyền truy cập cho BC: {bcId}\n";
            message += $"Tài khoản không có quyền: {unauthorizedCount} ({string.Join(", ", unauthorizedIds)})\n";
            message += $"Tài khoản có quyền: {authorizedCount} ({string.Join(", ", authorizedIds)})";

            return message;
        }
    }
}
