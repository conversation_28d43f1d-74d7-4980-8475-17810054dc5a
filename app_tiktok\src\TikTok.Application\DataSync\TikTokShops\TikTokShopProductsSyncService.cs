using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Consts;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Repositories;
using TikTokBusinessApi.Models;
using TikTokBusinessApi;
using Volo.Abp;
using Volo.Abp.Uow;
using TikTok.DataSync.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service implementation cho việc đồng bộ dữ liệu TikTok Shop Products
    /// </summary>
    public class TikTokShopProductsSyncService : BaseSyncService, ITikTokShopProductsSyncService
    {
        private const string SERVICE_NAME = "TikTokShopProductsSync";

        private readonly IRawTikTokShopProductsRepository _tikTokShopProductsRepository;
        private readonly IAssetRepository _assetRepository;
        private readonly IBusinessCenterRepository _businessCenterRepository;
        private readonly IRawGmvMaxCampaignsRepository _gmvMaxCampaignsRepository;

        /// <summary>
        /// Constructor
        /// </summary>
        public TikTokShopProductsSyncService(
            IServiceProvider serviceProvider,
            IRawTikTokShopProductsRepository tikTokShopProductsRepository,
            IAssetRepository assetRepository,
            IBusinessCenterRepository businessCenterRepository,
            IRawGmvMaxCampaignsRepository gmvMaxCampaignsRepository,
            ILogger<TikTokShopProductsSyncService> logger
            ) : base(serviceProvider, logger)
        {
            _tikTokShopProductsRepository = tikTokShopProductsRepository;
            _assetRepository = assetRepository;
            _businessCenterRepository = businessCenterRepository;
            _gmvMaxCampaignsRepository = gmvMaxCampaignsRepository;
        }

        /// <summary>
        /// Đồng bộ TikTok Shop Products theo Business Center ID
        /// Lấy tất cả advertiserId và storeId từ bảng RawGmvMaxIdentitiesEntity
        /// </summary>
        public async Task<TikTokShopProductsSyncResult> SyncTikTokShopProductsAsync(string bcId)
        {
            var result = new TikTokShopProductsSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ TikTok Shop Products cho BC: {BcId}", bcId);

                // Lấy danh sách campaigns từ GmvMax có StoreId
                var campaigns = await GetGmvMaxCampaignsWithStoreAsync(bcId);

                if (!campaigns.Any())
                {
                    _logger.LogDebug("Không tìm thấy campaign nào có StoreId cho BC: {BcId}", bcId);
                    result.EndTime = DateTime.UtcNow;
                    return result;
                }

                _logger.LogDebug("Tìm thấy {Count} campaign có StoreId cho BC: {BcId}", campaigns.Count, bcId);

                var unauthorizedAdvertiserIds = new List<string>();

                // Đồng bộ cho từng campaign (theo từng combination của advertiserId và storeId)
                var uniqueCombinations = campaigns
                    .GroupBy(c => new { c.AdvertiserId, c.StoreId })
                    .Select(g => g.First())
                    .ToList();

                foreach (var campaign in uniqueCombinations)
                {
                    try
                    {
                        var singleResult = await SyncTikTokShopProductsByIdentityAsync(
                            campaign.StoreId,
                            campaign.AdvertiserId,
                            bcId,
                            unauthorizedAdvertiserIds);

                        // Merge results
                        result.NewRecords += singleResult.NewRecords;
                        result.UpdatedRecords += singleResult.UpdatedRecords;
                        result.ErrorRecords += singleResult.ErrorRecords;
                        result.AvailableProductsCount += singleResult.AvailableProductsCount;
                        result.UnavailableProductsCount += singleResult.UnavailableProductsCount;
                        result.RunningCustomShopAdsCount += singleResult.RunningCustomShopAdsCount;
                        result.GmvMaxOccupiedCount += singleResult.GmvMaxOccupiedCount;
                        result.GmvMaxAvailableCount += singleResult.GmvMaxAvailableCount;
                        result.TotalPagesProcessed += singleResult.TotalPagesProcessed;
                        result.TotalProductsFromApi += singleResult.TotalProductsFromApi;

                        result.StoreCount++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ TikTok Shop Products cho Campaign: {AdvertiserId}, Store: {StoreId}",
                            campaign.AdvertiserId, campaign.StoreId);
                        result.ErrorRecords++;
                    }
                }

                result.AdvertiserCount = campaigns.Select(x => x.AdvertiserId).Distinct().Count();
                result.UnauthorizedAdvertiserIds = unauthorizedAdvertiserIds;
                result.EndTime = DateTime.UtcNow;

                _logger.LogDebug("Hoàn thành đồng bộ TikTok Shop Products cho BC: {BcId}, Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, Lỗi: {Error}",
                    bcId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.ErrorRecords);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                result.EndTime = DateTime.UtcNow;
                _logger.LogError(ex, "Lỗi nghiệp vụ khi đồng bộ TikTok Shop Products cho BC: {BcId}", bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ TikTok Shop Products: {ex.Message}";
                result.EndTime = DateTime.UtcNow;
                _logger.LogError(ex, "Lỗi khi đồng bộ TikTok Shop Products cho BC: {BcId}", bcId);
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ TikTok Shop Products theo Store ID (method gốc được giữ lại cho tương thích)
        /// </summary>
        public async Task<TikTokShopProductsSyncResult> SyncTikTokShopProductsByIdentityAsync(string storeId, string advertiserId, string bcId, List<string>? unauthorizedAdvertiserIds = null)
        {
            var result = new TikTokShopProductsSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ TikTok Shop Products cho Store: {StoreId}, Advertiser: {AdvertiserId}", storeId, advertiserId);

                // Tạo TikTok client
                using var tikTokClient = await CreateTikTokBusinessApiClient(bcId);

                // Lấy danh sách products từ API với pagination
                var allProducts = await GetProductsFromApiAsync(tikTokClient, storeId, advertiserId, bcId, result, unauthorizedAdvertiserIds);

                if (!allProducts.Any())
                {
                    _logger.LogDebug("Không có Product nào được trả về từ API cho Store: {StoreId}", storeId);
                    result.EndTime = DateTime.UtcNow;
                    return result;
                }

                _logger.LogDebug("Nhận được {Count} Product từ API cho Store: {StoreId}", allProducts.Count, storeId);

                // Xử lý và lưu dữ liệu
                await ProcessAndSaveProducts(allProducts, storeId, advertiserId, bcId, result);

                result.StoreCount = 1;
                result.AdvertiserCount = 1;
                result.EndTime = DateTime.UtcNow;

                _logger.LogDebug("Hoàn thành đồng bộ TikTok Shop Products cho Store: {StoreId}, Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, Lỗi: {Error}",
                    storeId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.ErrorRecords);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                result.EndTime = DateTime.UtcNow;
                _logger.LogError(ex, "Lỗi nghiệp vụ khi đồng bộ TikTok Shop Products cho Store: {StoreId}", storeId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ TikTok Shop Products: {ex.Message}";
                result.EndTime = DateTime.UtcNow;
                _logger.LogError(ex, "Lỗi khi đồng bộ TikTok Shop Products cho Store: {StoreId}", storeId);
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ TikTok Shop Products cho nhiều Store của một Advertiser
        /// </summary>
        public async Task<TikTokShopProductsSyncResult> SyncManyTikTokShopProductsAsync(string advertiserId, string bcId, List<string>? storeIds = null)
        {
            var result = new TikTokShopProductsSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                // Nếu storeIds là null, lấy tất cả store IDs từ asset repository
                if (storeIds == null)
                {
                    var stores = await _assetRepository.GetByBcIdAsync(bcId, assetType: Enums.AssetType.TIKTOK_SHOP);
                    storeIds = stores.Select(x => x.AssetId).ToList();
                    _logger.LogDebug("Lấy danh sách Store IDs từ Asset Repository cho BC: {BcId}, Số Store: {Count}", bcId, storeIds.Count);
                }

                if (!storeIds.Any())
                {
                    _logger.LogWarning("Không tìm thấy Store nào cho Advertiser: {AdvertiserId}", advertiserId);
                    result.ErrorMessage = $"Không tìm thấy Store nào cho Advertiser: {advertiserId}";
                    return result;
                }

                _logger.LogDebug("Bắt đầu đồng bộ nhiều TikTok Shop Products cho Advertiser: {AdvertiserId}, Số Store: {Count}", advertiserId, storeIds.Count);

                var unauthorizedAdvertiserIds = new List<string>();
                foreach (var storeId in storeIds)
                {
                    try
                    {
                        var singleResult = await SyncTikTokShopProductsByIdentityAsync(storeId, advertiserId, bcId, unauthorizedAdvertiserIds);

                        // Merge results
                        result.NewRecords += singleResult.NewRecords;
                        result.UpdatedRecords += singleResult.UpdatedRecords;
                        result.ErrorRecords += singleResult.ErrorRecords;
                        result.AvailableProductsCount += singleResult.AvailableProductsCount;
                        result.UnavailableProductsCount += singleResult.UnavailableProductsCount;
                        result.RunningCustomShopAdsCount += singleResult.RunningCustomShopAdsCount;
                        result.GmvMaxOccupiedCount += singleResult.GmvMaxOccupiedCount;
                        result.GmvMaxAvailableCount += singleResult.GmvMaxAvailableCount;
                        result.TotalPagesProcessed += singleResult.TotalPagesProcessed;
                        result.TotalProductsFromApi += singleResult.TotalProductsFromApi;

                        // Merge dictionaries
                        MergeDictionaries(result.ProductStatusCount, singleResult.ProductStatusCount);
                        MergeDictionaries(result.GmvMaxAdsStatusCount, singleResult.GmvMaxAdsStatusCount);
                        MergeDictionaries(result.CategoryCount, singleResult.CategoryCount);
                        MergeDictionaries(result.CurrencyCount, singleResult.CurrencyCount);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ TikTok Shop Products cho Store: {StoreId}", storeId);
                        result.ErrorRecords++;
                    }
                }

                result.StoreCount = storeIds.Count;
                result.AdvertiserCount = 1;
                result.UnauthorizedAdvertiserIds = unauthorizedAdvertiserIds;

                _logger.LogDebug("Hoàn thành đồng bộ nhiều TikTok Shop Products cho Advertiser: {AdvertiserId}, Tổng: {Total}", advertiserId, result.TotalSynced);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi nghiệp vụ khi đồng bộ nhiều TikTok Shop Products cho Advertiser: {AdvertiserId}", advertiserId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ nhiều TikTok Shop Products: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ nhiều TikTok Shop Products cho Advertiser: {AdvertiserId}", advertiserId);
            }

            result.EndTime = DateTime.UtcNow;
            return result;
        }

        /// <summary>
        /// Đồng bộ tất cả TikTok Shop Products cho tất cả Business Centers
        /// </summary>
        public async Task<TikTokShopProductsSyncResult> SyncAllTikTokShopProductsAsync()
        {
            var result = new TikTokShopProductsSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ tất cả TikTok Shop Products cho tất cả BC");

                // Lấy tất cả Business Centers
                var businessCenters = await _businessCenterRepository.GetListAsync();

                if (!businessCenters.Any())
                {
                    _logger.LogWarning("Không tìm thấy Business Center nào");
                    result.ErrorMessage = "Không tìm thấy Business Center nào";
                    return result;
                }

                foreach (var bc in businessCenters)
                {
                    try
                    {
                        // Lấy tất cả advertisers cho BC này
                        var advertisers = await _assetRepository.GetByBcIdAsync(bc.BcId, assetType: Enums.AssetType.ADVERTISER);

                        foreach (var advertiser in advertisers)
                        {
                            var bcResult = await SyncManyTikTokShopProductsAsync(advertiser.AssetId, bc.BcId);

                            // Merge results
                            result.NewRecords += bcResult.NewRecords;
                            result.UpdatedRecords += bcResult.UpdatedRecords;
                            result.ErrorRecords += bcResult.ErrorRecords;
                            result.StoreCount += bcResult.StoreCount;
                            result.AvailableProductsCount += bcResult.AvailableProductsCount;
                            result.UnavailableProductsCount += bcResult.UnavailableProductsCount;
                            result.RunningCustomShopAdsCount += bcResult.RunningCustomShopAdsCount;
                            result.GmvMaxOccupiedCount += bcResult.GmvMaxOccupiedCount;
                            result.GmvMaxAvailableCount += bcResult.GmvMaxAvailableCount;
                            result.TotalPagesProcessed += bcResult.TotalPagesProcessed;
                            result.TotalProductsFromApi += bcResult.TotalProductsFromApi;

                            // Merge dictionaries
                            MergeDictionaries(result.ProductStatusCount, bcResult.ProductStatusCount);
                            MergeDictionaries(result.GmvMaxAdsStatusCount, bcResult.GmvMaxAdsStatusCount);
                            MergeDictionaries(result.CategoryCount, bcResult.CategoryCount);
                            MergeDictionaries(result.CurrencyCount, bcResult.CurrencyCount);

                            result.UnauthorizedAdvertiserIds.AddRange(bcResult.UnauthorizedAdvertiserIds);
                        }

                        result.AdvertiserCount += advertisers.Count;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ TikTok Shop Products cho BC: {BcId}", bc.BcId);
                        result.ErrorRecords++;
                    }
                }

                result.BcCount = businessCenters.Count;
                _logger.LogDebug("Hoàn thành đồng bộ tất cả TikTok Shop Products cho tất cả BC, Tổng: {Total}", result.TotalSynced);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ tất cả TikTok Shop Products: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ tất cả TikTok Shop Products cho tất cả BC");
            }

            result.EndTime = DateTime.UtcNow;
            return result;
        }

        /// <summary>
        /// Lấy danh sách products từ TikTok API với pagination
        /// </summary>
        private async Task<List<StoreProduct>> GetProductsFromApiAsync(
            TikTokBusinessApiClient tikTokClient,
            string storeId,
            string advertiserId,
            string bcId,
            TikTokShopProductsSyncResult result,
            List<string>? unauthorizedAdvertiserIds)
        {
            var allProducts = new List<StoreProduct>();
            var page = 1;
            var pageSize = 100; // Batch size từ JSON response
            var hasMorePages = true;

            while (hasMorePages)
            {
                try
                {
                    _logger.LogDebug("Gọi API lấy products cho Store: {StoreId}, Page: {Page}, PageSize: {PageSize}",
                        storeId, page, pageSize);
                    var filtering = new StoreProductFiltering
                    {
                        // Có thể thêm điều kiện lọc nếu cần
                        AdCreationEligible = "GMV_MAX"
                    };

                    // Gọi API GetStoreProductsAsync với đúng tham số
                    var response = await tikTokClient.Store.GetStoreProductsAsync(
                        bcId,
                        storeId,
                        filtering: null,
                        advertiserId: advertiserId,
                        sortField: null,
                        sortType: null,
                        page: page,
                        pageSize: pageSize);

                    if (!TikTokApiCodes.IsSuccess(response.Code))
                    {
                        // Xử lý lỗi quyền truy cập
                        if (AdAccountSyncHelper.IsPermissionError(response) && unauthorizedAdvertiserIds != null)
                        {
                            var batchUnauthorizedIds = AdAccountSyncHelper.ParseUnauthorizedAdvertiserIds(response.Message);
                            unauthorizedAdvertiserIds.AddRange(batchUnauthorizedIds);
                        }
                        throw new BusinessException(response.Code.ToString(), response.Message);
                    }

                    if (response?.Data?.StoreProducts == null || !response.Data.StoreProducts.Any())
                    {
                        _logger.LogDebug("Không có thêm Product nào từ API cho Store: {StoreId}, Page: {Page}", storeId, page);
                        hasMorePages = false;
                        break;
                    }

                    var products = response.Data.StoreProducts;
                    allProducts.AddRange(products);
                    result.TotalProductsFromApi += products.Count;

                    _logger.LogDebug("Nhận được {Count} Product từ API cho Store: {StoreId}, Page: {Page}",
                        products.Count, storeId, page);

                    // Kiểm tra có trang tiếp theo không từ page_info
                    var pageInfo = response.Data.PageInfo;
                    if (pageInfo != null && pageInfo.TotalPage.HasValue)
                    {
                        hasMorePages = page < pageInfo.TotalPage.Value;
                    }
                    else
                    {
                        // Fallback: kiểm tra theo số lượng products
                        hasMorePages = products.Count >= pageSize;
                    }

                    page++;
                    result.TotalPagesProcessed++;

                    // Giới hạn số trang để tránh vòng lặp vô hạn
                    if (page > 1000)
                    {
                        _logger.LogWarning("Đã đạt giới hạn 1000 trang cho Store: {StoreId}", storeId);
                        break;
                    }
                }
                catch (BusinessException)
                {
                    throw; // Re-throw business exceptions
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Lỗi khi gọi API lấy products cho Store: {StoreId}, Page: {Page}", storeId, page);
                    throw;
                }
            }

            _logger.LogDebug("Tổng cộng nhận được {Count} Product từ API cho Store: {StoreId}", allProducts.Count, storeId);
            return allProducts;
        }

        /// <summary>
        /// Xử lý và lưu danh sách products theo pattern của AssetSyncService
        /// </summary>
        private async Task ProcessAndSaveProducts(
            List<StoreProduct> productList,
            string storeId,
            string advertiserId,
            string bcId,
            TikTokShopProductsSyncResult result)
        {
            using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false);

            try
            {
                var newEntities = new List<RawTikTokShopProductsEntity>();
                var updatedEntities = new List<RawTikTokShopProductsEntity>();

                // Convert dữ liệu từ API thành danh sách entity
                var mappedEntities = MapListApiDataToEntities(productList, storeId, advertiserId, bcId);

                // Lấy danh sách item group IDs để query DB
                var itemGroupIds = mappedEntities.Select(x => x.ItemGroupId).ToList();

                // Lấy danh sách đã có trong DB
                var existingEntities = await GetExistingEntitiesByStoreAndItemGroupIds(storeId, itemGroupIds);
                var existingDict = existingEntities.ToDictionary(x => x.ItemGroupId, x => x);

                // Duyệt từng bản ghi xử lý thêm mới/cập nhật
                foreach (var mappedEntity in mappedEntities)
                {
                    try
                    {
                        // Update statistics
                        UpdateResultStatistics(mappedEntity, result);

                        if (!existingDict.TryGetValue(mappedEntity.ItemGroupId, out var existingEntity))
                        {
                            // Thêm mới
                            mappedEntity.SyncedAt = DateTime.UtcNow;
                            newEntities.Add(mappedEntity);
                            result.NewRecords++;
                        }
                        else
                        {
                            // Cập nhật nếu có thay đổi
                            if (existingEntity.HasChanged(mappedEntity))
                            {
                                existingEntity.UpdateFromNewData(mappedEntity);
                                existingEntity.SyncedAt = DateTime.UtcNow;
                                updatedEntities.Add(existingEntity);
                                result.UpdatedRecords++;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi xử lý TikTok Shop Product: {ItemGroupId}", mappedEntity.ItemGroupId);
                        result.ErrorRecords++;
                    }
                }

                // Lưu các bản ghi mới
                if (newEntities.Any())
                {
                    await _tikTokShopProductsRepository.InsertManyAsync(newEntities);
                    _logger.LogDebug("Thêm mới {Count} TikTok Shop Product", newEntities.Count);
                }

                // Cập nhật các bản ghi đã tồn tại
                if (updatedEntities.Any())
                {
                    await _tikTokShopProductsRepository.UpdateManyAsync(updatedEntities);
                    _logger.LogDebug("Cập nhật {Count} TikTok Shop Product", updatedEntities.Count);
                }

                await uow.CompleteAsync();
                _logger.LogDebug("Hoàn thành xử lý và lưu {NewCount} mới, {UpdateCount} cập nhật TikTok Shop Product",
                    newEntities.Count, updatedEntities.Count);
            }
            catch (Exception ex)
            {
                await uow.RollbackAsync();
                _logger.LogError(ex, "Lỗi khi xử lý và lưu TikTok Shop Products cho Store: {StoreId}", storeId);
                throw;
            }
        }

        /// <summary>
        /// Lấy danh sách entity đã tồn tại theo store và item group IDs
        /// </summary>
        private async Task<List<RawTikTokShopProductsEntity>> GetExistingEntitiesByStoreAndItemGroupIds(string storeId, List<string> itemGroupIds)
        {
            var allEntities = await _tikTokShopProductsRepository.GetByStoreIdAsync(storeId);
            return allEntities.Where(x => itemGroupIds.Contains(x.ItemGroupId)).ToList();
        }

        /// <summary>
        /// Convert danh sách dữ liệu API thành danh sách entity
        /// </summary>
        private List<RawTikTokShopProductsEntity> MapListApiDataToEntities(List<StoreProduct> productList, string storeId, string advertiserId, string bcId)
        {
            var entities = new List<RawTikTokShopProductsEntity>();

            foreach (var product in productList)
            {
                try
                {
                    var entity = MapApiDataToEntity(product, storeId, advertiserId, bcId);
                    entities.Add(entity);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Lỗi khi map dữ liệu API sang Entity cho Product: {ItemGroupId}",
                        product.ItemGroupId);
                }
            }

            return entities;
        }

        /// <summary>
        /// Map dữ liệu từ API sang Entity
        /// </summary>
        private RawTikTokShopProductsEntity MapApiDataToEntity(StoreProduct product, string storeId, string advertiserId, string bcId)
        {
            var entity = new RawTikTokShopProductsEntity(Guid.NewGuid())
            {
                BcId = bcId,
                AdvertiserId = advertiserId,
                StoreId = storeId,
                ItemGroupId = product.ItemGroupId ?? string.Empty,
                Status = product.Status,
                ProductImageUrl = product.ProductImageUrl,
                CatalogId = product.CatalogId,
                Category = product.Category,
                MaxPrice = product.MaxPrice,
                MinPrice = product.MinPrice,
                Currency = product.Currency,
                Title = product.Title,
                GmvMaxAdsStatus = product.GmvMaxAdsStatus,
                HistoricalSales = product.HistoricalSales,
                IsRunningCustomShopAds = product.IsRunningCustomShopAds ?? false
            };

            return entity;
        }

        /// <summary>
        /// Lấy danh sách GmvMax campaigns có StoreId từ database
        /// </summary>
        private async Task<List<RawGmvMaxCampaignsEntity>> GetGmvMaxCampaignsWithStoreAsync(string bcId)
        {
            // Sử dụng method GetByBcIdAsync có sẵn trong repository
            var allCampaigns = await _gmvMaxCampaignsRepository.GetByBcIdAsync(bcId);

            // Lọc chỉ những campaign có StoreId
            var campaignsWithStore = allCampaigns
                .Where(x => !string.IsNullOrEmpty(x.StoreId))
                .ToList();

            _logger.LogDebug("Tìm thấy {TotalCount} campaign, trong đó {WithStoreCount} có StoreId cho BC: {BcId}",
                allCampaigns.Count, campaignsWithStore.Count, bcId);

            return campaignsWithStore;
        }

        /// <summary>
        /// Merge dictionaries
        /// </summary>
        private void MergeDictionaries(Dictionary<string, int> target, Dictionary<string, int> source)
        {
            foreach (var kvp in source)
            {
                if (target.ContainsKey(kvp.Key))
                    target[kvp.Key] += kvp.Value;
                else
                    target[kvp.Key] = kvp.Value;
            }
        }

        /// <summary>
        /// Cập nhật thống kê kết quả
        /// </summary>
        private void UpdateResultStatistics(RawTikTokShopProductsEntity entity, TikTokShopProductsSyncResult result)
        {
            // Count by status
            if (!string.IsNullOrEmpty(entity.Status))
            {
                if (result.ProductStatusCount.ContainsKey(entity.Status))
                    result.ProductStatusCount[entity.Status]++;
                else
                    result.ProductStatusCount[entity.Status] = 1;

                // Count available/unavailable
                if (entity.Status == "AVAILABLE")
                    result.AvailableProductsCount++;
                else
                    result.UnavailableProductsCount++;
            }

            // Count by GMV Max Ads Status
            if (!string.IsNullOrEmpty(entity.GmvMaxAdsStatus))
            {
                if (result.GmvMaxAdsStatusCount.ContainsKey(entity.GmvMaxAdsStatus))
                    result.GmvMaxAdsStatusCount[entity.GmvMaxAdsStatus]++;
                else
                    result.GmvMaxAdsStatusCount[entity.GmvMaxAdsStatus] = 1;

                // Count occupied/available
                if (entity.GmvMaxAdsStatus == "OCCUPIED")
                    result.GmvMaxOccupiedCount++;
                else if (entity.GmvMaxAdsStatus == "AVAILABLE")
                    result.GmvMaxAvailableCount++;
            }

            // Count by category
            if (!string.IsNullOrEmpty(entity.Category))
            {
                if (result.CategoryCount.ContainsKey(entity.Category))
                    result.CategoryCount[entity.Category]++;
                else
                    result.CategoryCount[entity.Category] = 1;
            }

            // Count by currency
            if (!string.IsNullOrEmpty(entity.Currency))
            {
                if (result.CurrencyCount.ContainsKey(entity.Currency))
                    result.CurrencyCount[entity.Currency]++;
                else
                    result.CurrencyCount[entity.Currency] = 1;
            }

            // Count running custom shop ads
            if (entity.IsRunningCustomShopAds)
                result.RunningCustomShopAdsCount++;
        }
    }
}
