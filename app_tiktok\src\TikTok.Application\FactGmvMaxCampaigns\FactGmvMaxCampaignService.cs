 using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Application.Services;
using TikTok.FactGmvMaxCampaigns.Dtos;
using TikTok.DimStores;
using TikTok.Domain.Entities.DataWarehouse.Fact;
using TikTok.Entities.Dim;
using TikTok.DimAdAccounts;
using TikTok.DimBusinessCenters;
using TikTok.DimCampaigns;
using TikTok.DimDates;
using Microsoft.Extensions.Logging;
using TikTok.Domain.Repositories;
using TikTok.Facts.FactGmvMaxCampaign;
using TikTok.Permissions;
using Volo.Abp.Authorization;
using Volo.Abp.Authorization.Permissions;

namespace TikTok.FactGmvMaxCampaigns
{
    public class FactGmvMaxCampaignService : ApplicationService, IFactGmvMaxCampaignService
    {
        private readonly IRepository<FactGmvMaxCampaignEntity, Guid> _factGmvMaxCampaignRepository;
        private readonly IRepository<DimDateEntity, int> _dimDateRepository;
        private readonly IRepository<DimBusinessCenterEntity, Guid> _dimBusinessCenterRepository;
        private readonly IRepository<DimAdAccountEntity, Guid> _dimAdAccountRepository;
        private readonly IRepository<DimCampaignEntity, Guid> _dimCampaignRepository;
        private readonly IRepository<DimStoreEntity, Guid> _dimStoreRepository;
        private readonly IFactGmvMaxCampaignDapperRepository _factGmvMaxCampaignDapperRepository;
        private readonly IPermissionChecker _permissionChecker;
        private readonly PermissionFieldHelper _permissionFieldHelper;

        public FactGmvMaxCampaignService(
            IRepository<FactGmvMaxCampaignEntity, Guid> factGmvMaxCampaignRepository,
            IRepository<DimDateEntity, int> dimDateRepository,
            IRepository<DimBusinessCenterEntity, Guid> dimBusinessCenterRepository,
            IRepository<DimAdAccountEntity, Guid> dimAdAccountRepository,
            IRepository<DimCampaignEntity, Guid> dimCampaignRepository,
            IRepository<DimStoreEntity, Guid> dimStoreRepository,
            IFactGmvMaxCampaignDapperRepository factGmvMaxCampaignDapperRepository,
            IPermissionChecker permissionChecker,
            PermissionFieldHelper permissionFieldHelper)
        {
            _factGmvMaxCampaignRepository = factGmvMaxCampaignRepository;
            _dimDateRepository = dimDateRepository;
            _dimBusinessCenterRepository = dimBusinessCenterRepository;
            _dimAdAccountRepository = dimAdAccountRepository;
            _dimCampaignRepository = dimCampaignRepository;
            _dimStoreRepository = dimStoreRepository;
            _factGmvMaxCampaignDapperRepository = factGmvMaxCampaignDapperRepository;
            _permissionChecker = permissionChecker;
            _permissionFieldHelper = permissionFieldHelper;
        }

        public async Task<GetFactGmvMaxCampaignDataResponse> GetListAsync(DateTime fromDate, DateTime toDate, string? currency = "USD", bool hasViewAllAdvertisers = false)
        {
            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);

                var result = new GetFactGmvMaxCampaignDataResponse()
                {
                    From = fromDate,
                    To = toDate,
                    Currency = currency,
                    FactGmvMaxCampaigns = new List<FactGmvMaxCampaignDto>(),
                    DimAdAccounts = new List<DimAdAccountDto>(),
                    DimBusinessCenters = new List<DimBusinessCenterDto>(),
                    DimCampaigns = new List<DimCampaignDto>(),
                    DimStores = new List<DimStoreDto>(),
                    DimDates = new List<DimDateDto>()
                };

                // ✅ Use Dapper repository with permission filtering
                var factGmvMaxCampaigns = await _factGmvMaxCampaignDapperRepository.GetListByDateRangeAsync(fromDate, toDate, allowedAdvertiserIds, currency);

                if (!factGmvMaxCampaigns.Any())
                {
                    // Return empty response if no data found
                    Logger.LogWarning($"No FactGmvMaxCampaign data found for date range {fromDate:yyyy-MM-dd} to {toDate:yyyy-MM-dd}");
                    return result;
                }

                // Map fact data
                var factList = factGmvMaxCampaigns is List<FactGmvMaxCampaignEntity> list ? list : new List<FactGmvMaxCampaignEntity>(factGmvMaxCampaigns);
                result.FactGmvMaxCampaigns = ObjectMapper.Map<List<FactGmvMaxCampaignEntity>, List<FactGmvMaxCampaignDto>>(factList);

                // Get all unique DimDateId values from factGmvMaxCampaigns
                var uniqueDimDateIds = factList.Select(f => f.DimDateId).Distinct().ToList();

                // Get all dim-dates that are included in factGmvMaxCampaigns data
                if (uniqueDimDateIds.Any())
                {
                    var dimDates = await _dimDateRepository.GetListAsync(d => uniqueDimDateIds.Contains(d.Id));
                    result.DimDates = ObjectMapper.Map<List<DimDateEntity>, List<DimDateDto>>(dimDates);
                }

                // Get all unique DimBusinessCenterId values from factGmvMaxCampaigns
                var uniqueDimBusinessCenterIds = factList.Select(f => f.DimBusinessCenterId).Distinct().ToList();

                // Get all dim-businesscenters that are included in factGmvMaxCampaigns data
                if (uniqueDimBusinessCenterIds.Any())
                {
                    var dimBusinessCenters = await _dimBusinessCenterRepository.GetListAsync(d => uniqueDimBusinessCenterIds.Contains(d.Id));
                    result.DimBusinessCenters = ObjectMapper.Map<List<DimBusinessCenterEntity>, List<DimBusinessCenterDto>>(dimBusinessCenters);
                }

                // Get all unique DimAdAccountId values from factGmvMaxCampaigns
                var uniqueDimAdAccountIds = factList.Select(f => f.DimAdAccountId).Distinct().ToList();

                // Get all dim-adaccounts that are included in factGmvMaxCampaigns data
                if (uniqueDimAdAccountIds.Any())
                {
                    var dimAdAccounts = await _dimAdAccountRepository.GetListAsync(d => uniqueDimAdAccountIds.Contains(d.Id));
                    result.DimAdAccounts = ObjectMapper.Map<List<DimAdAccountEntity>, List<DimAdAccountDto>>(dimAdAccounts);
                }

                // Get all unique DimCampaignId values from factGmvMaxCampaigns
                var uniqueDimCampaignIds = factList.Select(f => f.DimCampaignId).Distinct().ToList();

                // Get all dim-campaigns that are included in factGmvMaxCampaigns data
                if (uniqueDimCampaignIds.Any())
                {
                    var dimCampaigns = await _dimCampaignRepository.GetListAsync(d => uniqueDimCampaignIds.Contains(d.Id));
                    result.DimCampaigns = ObjectMapper.Map<List<DimCampaignEntity>, List<DimCampaignDto>>(dimCampaigns);
                }

                // Get all unique DimStoreId values from factGmvMaxCampaigns
                var uniqueDimStoreIds = factList.Select(f => f.DimStoreId).Distinct().ToList();

                // Get all dim-stores that are included in factGmvMaxCampaigns data
                if (uniqueDimStoreIds.Any())
                {
                    var dimStores = await _dimStoreRepository.GetListAsync(d => uniqueDimStoreIds.Contains(d.Id));
                    result.DimStores = ObjectMapper.Map<List<DimStoreEntity>, List<DimStoreDto>>(dimStores);
                }

                // Note: Removed DimProduct handling as it's no longer used in the updated entity structure

                Logger.LogInformation($"Successfully loaded {result.FactGmvMaxCampaigns.Count} GMV Max Campaign records and dimension data");

                return result;
            }
            catch (Exception ex)
            {
                // Log the exception (similar to FactBalanceService)
                Logger.LogError(ex, "Error retrieving FactGmvMaxCampaign data from database");
                throw new ValidationException($"Error retrieving GMV Max Campaign data: {ex.Message}");
            }
        }


        public async Task<GmvMaxCampaignDashboardDto> GetDashboardAsync(string? currency = "USD")
        {
            // ✅ Check permissions for dashboard access
            var hasViewSpending = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
            var hasViewMetrics = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
            var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
            var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

            if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
            {
                throw new AbpAuthorizationException("Bạn không có quyền truy cập dashboard GMV Max Campaign");
            }

            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);

                Logger.LogInformation("Fetching GMV Max Campaign dashboard data with currency: {Currency}", currency);
                var dashboardData = await _factGmvMaxCampaignDapperRepository.GetDashboardAsync(currency, allowedAdvertiserIds);
                
                // ✅ Filter dashboard data based on permissions
                var filteredData = FilterDashboardDataByPermissions(dashboardData, hasViewSpending, hasViewMetrics, hasViewAll);
                
                Logger.LogInformation("Successfully loaded GMV Max Campaign dashboard data");
                return filteredData;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error retrieving GMV Max Campaign dashboard data");
                throw new ValidationException($"Error retrieving dashboard data: {ex.Message}");
            }
        }

        public async Task<DashboardSummaryDto> GetDashboardSummaryAsync(string? currency = "USD")
        {
            // ✅ Check permissions for dashboard summary access
            var hasViewSpending = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
            var hasViewMetrics = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
            var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
            var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

            if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
            {
                throw new AbpAuthorizationException("Bạn không có quyền truy cập dashboard summary GMV Max Campaign");
            }

            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);

                Logger.LogInformation("Fetching GMV Max Campaign dashboard summary data with currency: {Currency}", currency);
                var summaryData = await _factGmvMaxCampaignDapperRepository.GetDashboardSummaryAsync(currency, allowedAdvertiserIds);
                
                // ✅ Filter summary data based on permissions
                var filteredSummary = FilterDashboardSummaryByPermissions(summaryData, hasViewSpending, hasViewMetrics, hasViewAll);
                
                Logger.LogInformation("Successfully loaded GMV Max Campaign dashboard summary data");
                return filteredSummary;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error retrieving GMV Max Campaign dashboard summary data");
                throw new ValidationException($"Error retrieving dashboard summary data: {ex.Message}");
            }
        }

        public async Task<object> GetDetailedAnalysisDataAsync()
        {
            // ✅ Check permissions for detailed analysis access
            var hasViewSpending = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
            var hasViewMetrics = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
            var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
            var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

            if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
            {
                throw new AbpAuthorizationException("Bạn không có quyền truy cập detailed analysis GMV Max Campaign");
            }

            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);

                Logger.LogInformation("Fetching GMV Max Campaign detailed analysis data for last 7 days");
                var detailedData = await _factGmvMaxCampaignDapperRepository.GetDetailedAnalysisDataAsync(allowedAdvertiserIds);
                
                // ✅ Filter detailed analysis data based on permissions
                var filteredData = FilterDetailedAnalysisByPermissions(detailedData, hasViewSpending, hasViewMetrics, hasViewAll);
                
                Logger.LogInformation("Successfully loaded GMV Max Campaign detailed analysis data");
                return filteredData;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error retrieving GMV Max Campaign detailed analysis data");
                throw new ValidationException($"Error retrieving detailed analysis data: {ex.Message}");
            }
        }

        public async Task<GetFactGmvMaxCampaignDataResponse> GetListWithPermissionsAsync(DateTime fromDate, DateTime toDate, string? currency = "USD")
        {
            try
            {
                // Check permissions
                var hasViewSpending = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
                var hasViewMetrics = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
                var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
                var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

                // If no permissions, throw authorization exception
                if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
                {
                    throw new AbpAuthorizationException("You don't have permission to view GMV Max Campaign data");
                }

                // Get data using existing method
                var result = await GetListAsync(fromDate, toDate, currency, hasViewAllAdvertisers);

                // Filter data based on permissions
                if (hasViewAll || hasViewAllAdvertisers)
                {
                    // ViewAll or ViewAllAdvertisers - return all data as is
                    Logger.LogInformation($"User has ViewAll or ViewAllAdvertisers permission - returning all {result.FactGmvMaxCampaigns.Count} records");
                    return result;
                }

                // Filter fields based on permissions
                var allowedFields = PermissionFieldHelper.GetAllowedFields(hasViewSpending, hasViewMetrics, hasViewAll);
                
                // Create filtered DTOs
                var filteredCampaigns = new List<FactGmvMaxCampaignDto>();
                foreach (var campaign in result.FactGmvMaxCampaigns)
                {
                    var filteredCampaign = FilterCampaignDtoByPermissions(campaign, allowedFields);
                    filteredCampaigns.Add(filteredCampaign);
                }

                result.FactGmvMaxCampaigns = filteredCampaigns;

                Logger.LogInformation($"Filtered GMV Max Campaign data based on permissions - returning {filteredCampaigns.Count} records with {allowedFields.Count} allowed fields");

                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error retrieving GMV Max Campaign data with permissions from {From} to {To}", fromDate, toDate);
                throw new ValidationException($"Error retrieving GMV Max Campaign data with permissions: {ex.Message}");
            }
        }

        /// <summary>
        /// Trả về bản ghi mới nhất cho mỗi chiến dịch trong khoảng thời gian (đã áp quyền)
        /// Dùng cho phân loại ROI tốt/xấu dựa trên snapshot mới nhất của campaign
        /// </summary>
        public async Task<List<FactGmvMaxCampaignDto>> GetLatestPerCampaignWithPermissionsAsync(
            DateTime fromDate,
            DateTime toDate,
            string? currency = "USD")
        {
            var result = await GetListWithPermissionsAsync(fromDate, toDate, currency);
            var all = result.FactGmvMaxCampaigns;

            if (all == null || all.Count == 0)
            {
                return new List<FactGmvMaxCampaignDto>();
            }

            // Chọn bản ghi mới nhất theo CampaignId (ưu tiên Date; fallback DimDateId nếu Date trùng/null)
            var latest = all
                .GroupBy(c => c.CampaignId)
                .Select(g => g
                    .OrderByDescending(x => x.Date)
                    .ThenByDescending(x => x.DimDateId)
                    .First())
                .ToList();

            return latest;
        }

        private FactGmvMaxCampaignDto FilterCampaignDtoByPermissions(FactGmvMaxCampaignDto campaign, HashSet<string> allowedFields)
        {
            // Clone the original campaign DTO
            var filteredCampaign = new FactGmvMaxCampaignDto();
            
            // Use reflection to copy only allowed fields
            var campaignType = typeof(FactGmvMaxCampaignDto);
            var properties = campaignType.GetProperties();
            
            foreach (var property in properties)
            {
                var propertyName = property.Name;
                
                // Copy field if it's allowed
                if (PermissionFieldHelper.IsFieldAllowed(propertyName, allowedFields))
                {
                    var value = property.GetValue(campaign);
                    property.SetValue(filteredCampaign, value);
                }
            }
            
            return filteredCampaign;
        }

        // ✅ Dashboard Data Filtering Methods
        private GmvMaxCampaignDashboardDto FilterDashboardDataByPermissions(GmvMaxCampaignDashboardDto data, bool hasViewSpending, bool hasViewMetrics, bool hasViewAll)
        {
            if (hasViewAll) return data;

            // Create a copy to avoid modifying original data
            var filteredData = new GmvMaxCampaignDashboardDto
            {
                CurrentMonth = data.CurrentMonth,
                LastMonth = data.LastMonth,
                WeeklyData = data.WeeklyData,
                MonthlyData = data.MonthlyData,
                CurrentWeekStoreRanking = data.CurrentWeekStoreRanking,
                TwoWeeksAgoStoreRanking = data.TwoWeeksAgoStoreRanking,
                OneWeekAgoStoreRanking = data.OneWeekAgoStoreRanking,
                CurrentWeekStoreCostRanking = data.CurrentWeekStoreCostRanking,
                TwoWeeksAgoStoreCostRanking = data.TwoWeeksAgoStoreCostRanking,
                OneWeekAgoStoreCostRanking = data.OneWeekAgoStoreCostRanking
            };

            // Filter based on permissions
            if (!hasViewSpending)
            {
                // Remove cost-related data
                filteredData.CurrentWeekStoreCostRanking = null;
                filteredData.TwoWeeksAgoStoreCostRanking = null;
                filteredData.OneWeekAgoStoreCostRanking = null;
            }

            if (!hasViewMetrics)
            {
                // Remove metrics-related data
                filteredData.CurrentWeekStoreRanking = null;
                filteredData.TwoWeeksAgoStoreRanking = null;
                filteredData.OneWeekAgoStoreRanking = null;
            }

            return filteredData;
        }

        private DashboardSummaryDto FilterDashboardSummaryByPermissions(DashboardSummaryDto summaryData, bool hasViewSpending, bool hasViewMetrics, bool hasViewAll)
        {
            if (hasViewAll) return summaryData;

            // Create filtered summary
            var filteredSummary = new DashboardSummaryDto
            {
                Month = summaryData.Month,
                Year = summaryData.Year,
                MonthName = summaryData.MonthName,
                FromDate = summaryData.FromDate,
                ToDate = summaryData.ToDate
            };

            // Check if spending fields should be visible
            if (hasViewSpending || hasViewAll)
            {
                filteredSummary.TotalCost = summaryData.TotalCost;
                filteredSummary.TotalNetCost = summaryData.TotalNetCost;
            }

            // Check if metrics fields should be visible
            if (hasViewMetrics || hasViewAll)
            {
                filteredSummary.TotalGrossRevenue = summaryData.TotalGrossRevenue;
                filteredSummary.AverageROAS = summaryData.AverageROAS;
                filteredSummary.AverageTACOS = summaryData.AverageTACOS;
                filteredSummary.TotalOrders = summaryData.TotalOrders;
            }

            // Always include basic fields
            filteredSummary.CampaignCount = summaryData.CampaignCount;
            filteredSummary.ActiveStores = summaryData.ActiveStores;
            filteredSummary.ActiveAdvertisers = summaryData.ActiveAdvertisers;
            filteredSummary.DataPointCount = summaryData.DataPointCount;

            return filteredSummary;
        }

        private object FilterDetailedAnalysisByPermissions(object detailedData, bool hasViewSpending, bool hasViewMetrics, bool hasViewAll)
        {
            if (hasViewAll) return detailedData;

            // For detailed analysis, we might need to filter arrays of objects
            // This is a simplified implementation - you may need to adjust based on actual data structure
            return detailedData;
        }

        // ✅ NEW: Section-specific methods for independent loading
        public async Task<SummaryCardsDto> GetSummaryCardsAsync(
            string? currency = "USD",
            string? shoppingAdsType = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            string? searchText = null,
            List<Guid>? shopIds = null)
        {
            // ✅ Check permissions for summary cards access
            var hasViewSpending = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
            var hasViewMetrics = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
            var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
            var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

            if (!hasViewSpending && !hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
            {
                throw new AbpAuthorizationException("Bạn không có quyền truy cập summary cards GMV Max Campaign");
            }

            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);

                Logger.LogInformation("Fetching GMV Max Campaign summary cards data with currency: {Currency}, shoppingAdsType: {ShoppingAdsType}", currency, shoppingAdsType);
                var summaryCards = await _factGmvMaxCampaignDapperRepository.GetSummaryCardsAsync(
                    currency, 
                    allowedAdvertiserIds, 
                    shoppingAdsType, 
                    fromDate, 
                    toDate, 
                    searchText, 
                    shopIds);
                
                Logger.LogInformation("Successfully loaded GMV Max Campaign summary cards data");
                return summaryCards;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error retrieving GMV Max Campaign summary cards data");
                throw new ValidationException($"Error retrieving summary cards data: {ex.Message}");
            }
        }

        public async Task<OverviewSectionDto> GetOverviewSectionAsync(string? currency = "USD")
        {
            // ✅ Check permissions for overview access
            var hasViewSpending = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
            var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
            var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

            if (!hasViewSpending && !hasViewAll && !hasViewAllAdvertisers)
            {
                throw new AbpAuthorizationException("Bạn không có quyền truy cập overview GMV Max Campaign");
            }

            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);

                Logger.LogInformation("Fetching GMV Max Campaign overview section data with currency: {Currency}", currency);
                var overview = await _factGmvMaxCampaignDapperRepository.GetOverviewSectionAsync(currency, allowedAdvertiserIds);
                
                
                Logger.LogInformation("Successfully loaded GMV Max Campaign overview section data");
                return overview;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error retrieving GMV Max Campaign overview section data");
                throw new ValidationException($"Error retrieving overview section data: {ex.Message}");
            }
        }

        public async Task<ChartsDataDto> GetChartsDataAsync(string? currency = "USD")
        {
            // ✅ Check permissions for charts access
            var hasViewSpending = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewSpending);
            var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
            var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

            if (!hasViewSpending && !hasViewAll && !hasViewAllAdvertisers)
            {
                throw new AbpAuthorizationException("Bạn không có quyền truy cập charts GMV Max Campaign");
            }

            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);

                Logger.LogInformation("Fetching GMV Max Campaign charts data with currency: {Currency}", currency);
                var chartsData = await _factGmvMaxCampaignDapperRepository.GetChartsDataAsync(currency, allowedAdvertiserIds);
                
                Logger.LogInformation("Successfully loaded GMV Max Campaign charts data");
                return chartsData;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error retrieving GMV Max Campaign charts data");
                throw new ValidationException($"Error retrieving charts data: {ex.Message}");
            }
        }

        public async Task<DetailedChartsDto> GetDetailedChartsAsync()
        {
            // ✅ Check permissions for detailed charts access
            var hasViewMetrics = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewMetrics);
            var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
            var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

            if (!hasViewMetrics && !hasViewAll && !hasViewAllAdvertisers)
            {
                throw new AbpAuthorizationException("Bạn không có quyền truy cập detailed charts GMV Max Campaign");
            }

            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);

                Logger.LogInformation("Fetching GMV Max Campaign detailed charts data for last 7 days");
                var detailedCharts = await _factGmvMaxCampaignDapperRepository.GetDetailedChartsAsync(allowedAdvertiserIds);
                
                Logger.LogInformation("Successfully loaded GMV Max Campaign detailed charts data");
                return detailedCharts;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error retrieving GMV Max Campaign detailed charts data");
                throw new ValidationException($"Error retrieving detailed charts data: {ex.Message}");
            }
        }

        public async Task<RankingsDataDto> GetRankingsDataAsync(string? currency = "USD")
        {
            // ✅ Check permissions for rankings access - requires ViewAll or ViewAllAdvertisers
            var hasViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAll);
            var hasViewAllAdvertisers = await _permissionChecker.IsGrantedAsync(TikTokPermissions.FactGmvMax.ViewAllAdvertisers);

            if (!hasViewAll && !hasViewAllAdvertisers)
            {
                throw new AbpAuthorizationException("Bạn không có quyền truy cập rankings GMV Max Campaign");
            }

            try
            {
                // ✅ Get user's allowed advertiser IDs for permission filtering
                var allowedAdvertiserIds = await _permissionFieldHelper.GetUserAllowedAdvertiserIdsAsync(hasViewAllAdvertisers);

                Logger.LogInformation("Fetching GMV Max Campaign rankings data with currency: {Currency}", currency);
                var rankingsData = await _factGmvMaxCampaignDapperRepository.GetRankingsDataAsync(currency, allowedAdvertiserIds);
                
                
                Logger.LogInformation("Successfully loaded GMV Max Campaign rankings data");
                return rankingsData;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error retrieving GMV Max Campaign rankings data");
                throw new ValidationException($"Error retrieving rankings data: {ex.Message}");
            }
        }


    }
}


