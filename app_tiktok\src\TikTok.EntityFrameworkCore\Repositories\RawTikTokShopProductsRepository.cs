using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using TikTok.Entities;
using TikTok.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace TikTok.Repositories
{
    /// <summary>
    /// Repository implementation cho RawTikTokShopProductsEntity
    /// </summary>
    public class RawTikTokShopProductsRepository : EfCoreRepository<TikTokDbContext, RawTikTokShopProductsEntity, Guid>, IRawTikTokShopProductsRepository
    {
        public RawTikTokShopProductsRepository(IDbContextProvider<TikTokDbContext> dbContextProvider) : base(dbContextProvider)
        {
        }

        /// <summary>
        /// Lấy danh sách products theo Store ID
        /// </summary>
        public async Task<List<RawTikTokShopProductsEntity>> GetByStoreIdAsync(string storeId, CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => x.StoreId == storeId)
                .OrderBy(x => x.ItemGroupId)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Lấy danh sách products theo Advertiser ID
        /// </summary>
        public async Task<List<RawTikTokShopProductsEntity>> GetByAdvertiserIdAsync(string advertiserId, CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => x.AdvertiserId == advertiserId)
                .OrderBy(x => x.StoreId)
                .ThenBy(x => x.ItemGroupId)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Lấy danh sách products theo BC ID
        /// </summary>
        public async Task<List<RawTikTokShopProductsEntity>> GetByBcIdAsync(string bcId, CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => x.BcId == bcId)
                .OrderBy(x => x.AdvertiserId)
                .ThenBy(x => x.StoreId)
                .ThenBy(x => x.ItemGroupId)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Lấy danh sách products theo trạng thái
        /// </summary>
        public async Task<List<RawTikTokShopProductsEntity>> GetByStatusAsync(string status, CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => x.Status == status)
                .OrderBy(x => x.StoreId)
                .ThenBy(x => x.ItemGroupId)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Lấy danh sách products có sẵn (status = AVAILABLE)
        /// </summary>
        public async Task<List<RawTikTokShopProductsEntity>> GetAvailableProductsAsync(string? storeId = null, CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            var query = dbSet.Where(x => x.Status == "AVAILABLE");
            
            if (!string.IsNullOrEmpty(storeId))
            {
                query = query.Where(x => x.StoreId == storeId);
            }
            
            return await query
                .OrderBy(x => x.StoreId)
                .ThenBy(x => x.ItemGroupId)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Lấy danh sách products theo GMV Max Ads Status
        /// </summary>
        public async Task<List<RawTikTokShopProductsEntity>> GetByGmvMaxAdsStatusAsync(string gmvMaxAdsStatus, string? storeId = null, CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            var query = dbSet.Where(x => x.GmvMaxAdsStatus == gmvMaxAdsStatus);
            
            if (!string.IsNullOrEmpty(storeId))
            {
                query = query.Where(x => x.StoreId == storeId);
            }
            
            return await query
                .OrderBy(x => x.StoreId)
                .ThenBy(x => x.ItemGroupId)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Lấy danh sách products đang chạy custom shop ads
        /// </summary>
        public async Task<List<RawTikTokShopProductsEntity>> GetRunningCustomShopAdsAsync(string? storeId = null, CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            var query = dbSet.Where(x => x.IsRunningCustomShopAds);
            
            if (!string.IsNullOrEmpty(storeId))
            {
                query = query.Where(x => x.StoreId == storeId);
            }
            
            return await query
                .OrderBy(x => x.StoreId)
                .ThenBy(x => x.ItemGroupId)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// Lấy product theo Item Group ID
        /// </summary>
        public async Task<RawTikTokShopProductsEntity?> GetByItemGroupIdAsync(string itemGroupId, string storeId, CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => x.ItemGroupId == itemGroupId && x.StoreId == storeId)
                .FirstOrDefaultAsync(cancellationToken);
        }

        /// <summary>
        /// Kiểm tra product có tồn tại không
        /// </summary>
        public async Task<bool> ExistsAsync(string itemGroupId, string storeId, CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Where(x => x.ItemGroupId == itemGroupId && x.StoreId == storeId)
                .AnyAsync(cancellationToken);
        }

        /// <summary>
        /// Xóa tất cả products của một Store
        /// </summary>
        public async Task<int> DeleteByStoreIdAsync(string storeId, CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            var entities = await dbSet
                .Where(x => x.StoreId == storeId)
                .ToListAsync(cancellationToken);
            
            if (entities.Any())
            {
                dbSet.RemoveRange(entities);
                await (await GetDbContextAsync()).SaveChangesAsync(cancellationToken);
            }
            
            return entities.Count;
        }

        /// <summary>
        /// Xóa tất cả products của một Advertiser
        /// </summary>
        public async Task<int> DeleteByAdvertiserIdAsync(string advertiserId, CancellationToken cancellationToken = default)
        {
            var dbSet = await GetDbSetAsync();
            var entities = await dbSet
                .Where(x => x.AdvertiserId == advertiserId)
                .ToListAsync(cancellationToken);
            
            if (entities.Any())
            {
                dbSet.RemoveRange(entities);
                await (await GetDbContextAsync()).SaveChangesAsync(cancellationToken);
            }
            
            return entities.Count;
        }
    }
}
