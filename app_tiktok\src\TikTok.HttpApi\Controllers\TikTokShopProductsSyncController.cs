using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using TikTok.DataSync;
using Volo.Abp.AspNetCore.Mvc;

namespace TikTok.Controllers
{
    /// <summary>
    /// Controller cho TikTok Shop Products Sync API
    /// </summary>
    [ApiController]
    [Route("api/app/tiktok-shop-products-sync")]
    public class TikTokShopProductsSyncController : AbpControllerBase
    {
        private readonly TikTokShopProductsSyncApplicationAppService _tikTokShopProductsSyncAppService;

        /// <summary>
        /// Constructor
        /// </summary>
        public TikTokShopProductsSyncController(TikTokShopProductsSyncApplicationAppService tikTokShopProductsSyncAppService)
        {
            _tikTokShopProductsSyncAppService = tikTokShopProductsSyncAppService;
        }

        /// <summary>
        /// Đồng bộ TikTok Shop Products theo Business Center ID
        /// Lấy advertiserId và storeId từ bảng RawGmvMaxIdentitiesEntity
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        [HttpPost("sync")]
        public async Task<TikTokShopProductsSyncResult> SyncTikTokShopProductsAsync(
            [Required] string bcId)
        {
            return await _tikTokShopProductsSyncAppService.SyncTikTokShopProductsAsync(bcId);
        }

        /// <summary>
        /// Đồng bộ TikTok Shop Products theo Store ID (được giữ lại cho tương thích)
        /// </summary>
        /// <param name="storeId">ID của Store</param>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        [HttpPost("sync-by-identity")]
        public async Task<TikTokShopProductsSyncResult> SyncTikTokShopProductsByIdentityAsync(
            [Required] string storeId,
            [Required] string advertiserId,
            [Required] string bcId)
        {
            return await _tikTokShopProductsSyncAppService.SyncTikTokShopProductsByIdentityAsync(storeId, advertiserId, bcId);
        }

        /// <summary>
        /// Đồng bộ TikTok Shop Products cho nhiều Store của một Advertiser
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="storeIds">Danh sách Store IDs (null để đồng bộ tất cả)</param>
        /// <returns>Kết quả đồng bộ</returns>
        [HttpPost("sync-many")]
        public async Task<TikTokShopProductsSyncResult> SyncManyTikTokShopProductsAsync(
            [Required] string advertiserId,
            [Required] string bcId,
            [FromBody] List<string>? storeIds = null)
        {
            return await _tikTokShopProductsSyncAppService.SyncManyTikTokShopProductsAsync(advertiserId, bcId, storeIds);
        }

        /// <summary>
        /// Đồng bộ tất cả TikTok Shop Products cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        [HttpPost("sync-all")]
        public async Task<TikTokShopProductsSyncResult> SyncAllTikTokShopProductsAsync()
        {
            return await _tikTokShopProductsSyncAppService.SyncAllTikTokShopProductsAsync();
        }

        /// <summary>
        /// Lấy thống kê TikTok Shop Products theo Store ID
        /// </summary>
        /// <param name="storeId">ID của Store</param>
        /// <returns>Thống kê products</returns>
        [HttpGet("stats/store/{storeId}")]
        public async Task<object> GetStoreProductsStatsAsync([Required] string storeId)
        {
            // TODO: Implement statistics endpoint if needed
            return new { Message = "Statistics endpoint not implemented yet", StoreId = storeId };
        }

        /// <summary>
        /// Lấy thống kê TikTok Shop Products theo Advertiser ID
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <returns>Thống kê products</returns>
        [HttpGet("stats/advertiser/{advertiserId}")]
        public async Task<object> GetAdvertiserProductsStatsAsync([Required] string advertiserId)
        {
            // TODO: Implement statistics endpoint if needed
            return new { Message = "Statistics endpoint not implemented yet", AdvertiserId = advertiserId };
        }

        /// <summary>
        /// Lấy thống kê TikTok Shop Products theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Thống kê products</returns>
        [HttpGet("stats/bc/{bcId}")]
        public async Task<object> GetBcProductsStatsAsync([Required] string bcId)
        {
            // TODO: Implement statistics endpoint if needed
            return new { Message = "Statistics endpoint not implemented yet", BcId = bcId };
        }
    }
}
