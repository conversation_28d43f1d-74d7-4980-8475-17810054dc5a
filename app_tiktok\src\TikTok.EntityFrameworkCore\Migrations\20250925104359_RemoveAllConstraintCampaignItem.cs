﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TikTok.Migrations
{
    /// <inheritdoc />
    public partial class RemoveAllConstraintCampaignItem : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Raw_RawGmvMaxCampaignItems_CampaignId",
                table: "Raw_RawGmvMaxCampaignItems");

            migrationBuilder.DropIndex(
                name: "IX_Raw_RawGmvMaxCampaignItems_CampaignId_ItemId",
                table: "Raw_RawGmvMaxCampaignItems");

            migrationBuilder.DropIndex(
                name: "IX_Raw_RawGmvMaxCampaignItems_IdentityId",
                table: "Raw_RawGmvMaxCampaignItems");

            migrationBuilder.DropIndex(
                name: "IX_Raw_RawGmvMaxCampaignItems_IdentityType",
                table: "Raw_RawGmvMaxCampaignItems");

            migrationBuilder.DropIndex(
                name: "IX_Raw_RawGmvMaxCampaignItems_ItemId",
                table: "Raw_RawGmvMaxCampaignItems");

            migrationBuilder.DropIndex(
                name: "IX_Raw_RawGmvMaxCampaignItems_UserName",
                table: "Raw_RawGmvMaxCampaignItems");

            migrationBuilder.DropIndex(
                name: "IX_Raw_RawGmvMaxCampaignItems_VideoId",
                table: "Raw_RawGmvMaxCampaignItems");

            migrationBuilder.DropIndex(
                name: "IX_Raw_RawGmvMaxCampaignIdentities_CampaignId",
                table: "Raw_RawGmvMaxCampaignIdentities");

            migrationBuilder.DropIndex(
                name: "IX_Raw_RawGmvMaxCampaignIdentities_CampaignId_IdentityId",
                table: "Raw_RawGmvMaxCampaignIdentities");

            migrationBuilder.DropIndex(
                name: "IX_Raw_RawGmvMaxCampaignIdentities_IdentityAuthorizedBcId",
                table: "Raw_RawGmvMaxCampaignIdentities");

            migrationBuilder.DropIndex(
                name: "IX_Raw_RawGmvMaxCampaignIdentities_IdentityId",
                table: "Raw_RawGmvMaxCampaignIdentities");

            migrationBuilder.DropIndex(
                name: "IX_Raw_RawGmvMaxCampaignIdentities_IdentityType",
                table: "Raw_RawGmvMaxCampaignIdentities");

            migrationBuilder.DropIndex(
                name: "IX_Raw_RawGmvMaxCampaignIdentities_StoreId",
                table: "Raw_RawGmvMaxCampaignIdentities");

            migrationBuilder.DropIndex(
                name: "IX_Raw_RawGmvMaxCampaignCustomAnchorVideos_CampaignId",
                table: "Raw_RawGmvMaxCampaignCustomAnchorVideos");

            migrationBuilder.DropIndex(
                name: "IX_Raw_RawGmvMaxCampaignCustomAnchorVideos_CampaignId_ItemId",
                table: "Raw_RawGmvMaxCampaignCustomAnchorVideos");

            migrationBuilder.DropIndex(
                name: "IX_Raw_RawGmvMaxCampaignCustomAnchorVideos_IdentityAuthorizedBcId",
                table: "Raw_RawGmvMaxCampaignCustomAnchorVideos");

            migrationBuilder.DropIndex(
                name: "IX_Raw_RawGmvMaxCampaignCustomAnchorVideos_IdentityId",
                table: "Raw_RawGmvMaxCampaignCustomAnchorVideos");

            migrationBuilder.DropIndex(
                name: "IX_Raw_RawGmvMaxCampaignCustomAnchorVideos_IdentityStoreId",
                table: "Raw_RawGmvMaxCampaignCustomAnchorVideos");

            migrationBuilder.DropIndex(
                name: "IX_Raw_RawGmvMaxCampaignCustomAnchorVideos_IdentityType",
                table: "Raw_RawGmvMaxCampaignCustomAnchorVideos");

            migrationBuilder.DropIndex(
                name: "IX_Raw_RawGmvMaxCampaignCustomAnchorVideos_ItemId",
                table: "Raw_RawGmvMaxCampaignCustomAnchorVideos");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_Raw_RawGmvMaxCampaignItems_CampaignId",
                table: "Raw_RawGmvMaxCampaignItems",
                column: "CampaignId");

            migrationBuilder.CreateIndex(
                name: "IX_Raw_RawGmvMaxCampaignItems_CampaignId_ItemId",
                table: "Raw_RawGmvMaxCampaignItems",
                columns: new[] { "CampaignId", "ItemId" });

            migrationBuilder.CreateIndex(
                name: "IX_Raw_RawGmvMaxCampaignItems_IdentityId",
                table: "Raw_RawGmvMaxCampaignItems",
                column: "IdentityId");

            migrationBuilder.CreateIndex(
                name: "IX_Raw_RawGmvMaxCampaignItems_IdentityType",
                table: "Raw_RawGmvMaxCampaignItems",
                column: "IdentityType");

            migrationBuilder.CreateIndex(
                name: "IX_Raw_RawGmvMaxCampaignItems_ItemId",
                table: "Raw_RawGmvMaxCampaignItems",
                column: "ItemId");

            migrationBuilder.CreateIndex(
                name: "IX_Raw_RawGmvMaxCampaignItems_UserName",
                table: "Raw_RawGmvMaxCampaignItems",
                column: "UserName");

            migrationBuilder.CreateIndex(
                name: "IX_Raw_RawGmvMaxCampaignItems_VideoId",
                table: "Raw_RawGmvMaxCampaignItems",
                column: "VideoId");

            migrationBuilder.CreateIndex(
                name: "IX_Raw_RawGmvMaxCampaignIdentities_CampaignId",
                table: "Raw_RawGmvMaxCampaignIdentities",
                column: "CampaignId");

            migrationBuilder.CreateIndex(
                name: "IX_Raw_RawGmvMaxCampaignIdentities_CampaignId_IdentityId",
                table: "Raw_RawGmvMaxCampaignIdentities",
                columns: new[] { "CampaignId", "IdentityId" });

            migrationBuilder.CreateIndex(
                name: "IX_Raw_RawGmvMaxCampaignIdentities_IdentityAuthorizedBcId",
                table: "Raw_RawGmvMaxCampaignIdentities",
                column: "IdentityAuthorizedBcId");

            migrationBuilder.CreateIndex(
                name: "IX_Raw_RawGmvMaxCampaignIdentities_IdentityId",
                table: "Raw_RawGmvMaxCampaignIdentities",
                column: "IdentityId");

            migrationBuilder.CreateIndex(
                name: "IX_Raw_RawGmvMaxCampaignIdentities_IdentityType",
                table: "Raw_RawGmvMaxCampaignIdentities",
                column: "IdentityType");

            migrationBuilder.CreateIndex(
                name: "IX_Raw_RawGmvMaxCampaignIdentities_StoreId",
                table: "Raw_RawGmvMaxCampaignIdentities",
                column: "StoreId");

            migrationBuilder.CreateIndex(
                name: "IX_Raw_RawGmvMaxCampaignCustomAnchorVideos_CampaignId",
                table: "Raw_RawGmvMaxCampaignCustomAnchorVideos",
                column: "CampaignId");

            migrationBuilder.CreateIndex(
                name: "IX_Raw_RawGmvMaxCampaignCustomAnchorVideos_CampaignId_ItemId",
                table: "Raw_RawGmvMaxCampaignCustomAnchorVideos",
                columns: new[] { "CampaignId", "ItemId" });

            migrationBuilder.CreateIndex(
                name: "IX_Raw_RawGmvMaxCampaignCustomAnchorVideos_IdentityAuthorizedBcId",
                table: "Raw_RawGmvMaxCampaignCustomAnchorVideos",
                column: "IdentityAuthorizedBcId");

            migrationBuilder.CreateIndex(
                name: "IX_Raw_RawGmvMaxCampaignCustomAnchorVideos_IdentityId",
                table: "Raw_RawGmvMaxCampaignCustomAnchorVideos",
                column: "IdentityId");

            migrationBuilder.CreateIndex(
                name: "IX_Raw_RawGmvMaxCampaignCustomAnchorVideos_IdentityStoreId",
                table: "Raw_RawGmvMaxCampaignCustomAnchorVideos",
                column: "IdentityStoreId");

            migrationBuilder.CreateIndex(
                name: "IX_Raw_RawGmvMaxCampaignCustomAnchorVideos_IdentityType",
                table: "Raw_RawGmvMaxCampaignCustomAnchorVideos",
                column: "IdentityType");

            migrationBuilder.CreateIndex(
                name: "IX_Raw_RawGmvMaxCampaignCustomAnchorVideos_ItemId",
                table: "Raw_RawGmvMaxCampaignCustomAnchorVideos",
                column: "ItemId");
        }
    }
}
