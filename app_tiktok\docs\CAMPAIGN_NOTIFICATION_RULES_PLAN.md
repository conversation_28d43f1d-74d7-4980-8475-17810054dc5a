# Simple Campaign Notification Rules System - <PERSON>ế hoạch Triển khai

## 📋 Tổng quan Dự án

### Mục tiêu

Xây dựng hệ thống rule thông báo **đơn giản** cho GMV Max Campaign và Product data, cho phép users dễ dàng tạo rules với **3 bước đơn giản**: chọn entity → chọn field → set điều kiện.

### Đặc điểm chính

-   **Simple Rule Engine**: 1 field + 1 operator + 1 value per rule
-   **Multi-Entity Support**: Campaign rules và Product rules
-   **Simple Template System**: Default templates + optional custom templates
-   **Easy UI**: 4 form fields đơn giản thay vì complex QueryBuilder
-   **Quick Setup**: 2-3 phút tạo 1 rule thay vì 45-60 phút
-   **Backward Compatible**: Tích hợp với hệ thống notification hiện tại

---

## 🏗️ Architecture Overview

### Simple System Integration

```
Simple Flow:
Manual Trigger (by user) → Simple Rule Engine → Default/Custom Template → TikTokNotificationService (reuse)
```

### Core Components

1. **Simple Rule Engine** - Xử lý single condition evaluation
2. **Field Definition Registry** - Predefined top 10-15 fields per entity
3. **Simple Template System** - Default templates với optional customization
4. **Simple Rule Builder UI** - 4 form fields đơn giản

---

## 📊 Data Model

### Supported Fields (Top Fields Only)

#### GMV Max Campaign (Top 10 fields)

-   **Performance**: `ROAS`, `TACOS`, `Cost`, `GrossRevenue`, `Orders`
-   **Basic Info**: `CampaignName`, `ShoppingAdsType`, `OperationStatus`
-   **Account**: `TtAccountName`, `IdentityId`

#### GMV Max Product (Top 10 fields)

-   **Performance**: `Orders`, `GrossRevenue`, `ProductClickRate`
-   **Creative**: `AdConversionRate`, `CreativeDeliveryStatus`
-   **Product Info**: `ProductName`, `ProductStatus`, `CreativeType`
-   **Account**: `TtAccountName`, `TtAccountAuthorizationType`

---

## 🛠️ Technical Implementation

### Database Schema

#### Core Entity

**Simple Notification Rule Entity**

```csharp
public class SimpleNotificationRule : FullAuditedEntity<Guid>
{
    public string RuleName { get; set; }
    public string EntityType { get; set; } // "Campaign", "Product"

    // Simple condition - chỉ 1 field, 1 operator, 1 value
    public string FieldName { get; set; }        // "ROAS", "Cost", "Orders"
    public string Operator { get; set; }         // "LessThan", "GreaterThan", "Equal"
    public string Value { get; set; }            // "2.0", "100", "0"

    // Simple template - optional
    public string CustomTitle { get; set; }      // null = use default
    public string CustomMessage { get; set; }    // null = use default

    public bool IsActive { get; set; } = true;
    public string RecipientUserIds { get; set; } // JSON array - optional, null = all users
}
```

### Field Definitions

**Predefined Field Registry**

```csharp
public static class FieldDefinitions
{
    public static readonly Dictionary<string, List<FieldMetadata>> EntityFields = new()
    {
        ["Campaign"] = new List<FieldMetadata>
        {
            new("ROAS", "ROAS (Tỷ lệ lợi nhuận)", "decimal", new[] { "LessThan", "GreaterThan" }),
            new("TACOS", "TACOS (% chi phí quảng cáo)", "decimal", new[] { "LessThan", "GreaterThan" }),
            new("Cost", "Chi phí quảng cáo", "decimal", new[] { "LessThan", "GreaterThan" }),
            new("GrossRevenue", "Tổng doanh thu", "decimal", new[] { "LessThan", "GreaterThan" }),
            new("Orders", "Số đơn hàng", "int", new[] { "Equal", "LessThan", "GreaterThan" }),
            new("CampaignName", "Tên Campaign", "string", new[] { "Equal" }),
            new("ShoppingAdsType", "Loại Campaign", "string", new[] { "Equal" }),
            new("OperationStatus", "Trạng thái hoạt động", "string", new[] { "Equal" }),
            new("TtAccountName", "Tên TikTok Account", "string", new[] { "Equal" }),
            new("IdentityId", "Identity ID", "string", new[] { "Equal" })
        },
        ["Product"] = new List<FieldMetadata>
        {
            new("Orders", "Số đơn hàng", "int", new[] { "Equal", "LessThan", "GreaterThan" }),
            new("GrossRevenue", "Doanh thu", "decimal", new[] { "LessThan", "GreaterThan" }),
            new("ProductClickRate", "Tỷ lệ click sản phẩm", "decimal", new[] { "LessThan", "GreaterThan" }),
            new("AdConversionRate", "Tỷ lệ chuyển đổi", "decimal", new[] { "LessThan", "GreaterThan" }),
            new("CreativeDeliveryStatus", "Trạng thái Creative", "string", new[] { "Equal" }),
            new("ProductName", "Tên sản phẩm", "string", new[] { "Equal" }),
            new("ProductStatus", "Trạng thái sản phẩm", "string", new[] { "Equal" }),
            new("CreativeType", "Loại Creative", "string", new[] { "Equal" }),
            new("TtAccountName", "Tên TikTok Account", "string", new[] { "Equal" }),
            new("TtAccountAuthorizationType", "Loại ủy quyền", "string", new[] { "Equal" })
        }
    };
}

public record FieldMetadata(string Name, string DisplayName, string DataType, string[] SupportedOperators);
```

### Backend Components

**Simple Rule Engine**

```csharp
public class SimpleRuleEngine : ITransientDependency
{
    public async Task<List<NotificationRequest>> EvaluateSimpleRules<T>(
        IEnumerable<T> entities,
        string entityType)
    {
        var notifications = new List<NotificationRequest>();
        var rules = await _ruleRepository.GetActiveRulesByEntityType(entityType);

        foreach (var rule in rules)
        {
            var matchingEntities = EvaluateSimpleCondition(rule, entities);

            foreach (var entity in matchingEntities)
            {
                var notification = await BuildSimpleNotification(rule, entity);
                notifications.Add(notification);
            }
        }

        return notifications;
    }

    private List<T> EvaluateSimpleCondition<T>(SimpleNotificationRule rule, IEnumerable<T> entities)
    {
        return entities.Where(entity =>
        {
            var fieldValue = GetFieldValue(entity, rule.FieldName);
            return EvaluateOperator(fieldValue, rule.Operator, rule.Value);
        }).ToList();
    }

    private bool EvaluateOperator(object fieldValue, string @operator, string value)
    {
        if (fieldValue == null) return false;

        return @operator switch
        {
            "LessThan" => Convert.ToDecimal(fieldValue) < Convert.ToDecimal(value),
            "GreaterThan" => Convert.ToDecimal(fieldValue) > Convert.ToDecimal(value),
            "Equal" => fieldValue.ToString().Equals(value, StringComparison.OrdinalIgnoreCase),
            _ => false
        };
    }

    private object GetFieldValue<T>(T entity, string fieldName)
    {
        var property = typeof(T).GetProperty(fieldName);
        return property?.GetValue(entity);
    }
}
```

**Simple Template Renderer**

```csharp
public class SimpleTemplateRenderer : ITransientDependency
{
    public async Task<NotificationRequest> BuildSimpleNotification<T>(
        SimpleNotificationRule rule,
        T entity)
    {
        // Use custom template or default
        var title = !string.IsNullOrEmpty(rule.CustomTitle)
            ? RenderSimpleTemplate(rule.CustomTitle, entity)
            : GetDefaultTitle(rule, entity);

        var message = !string.IsNullOrEmpty(rule.CustomMessage)
            ? RenderSimpleTemplate(rule.CustomMessage, entity)
            : GetDefaultMessage(rule, entity);

        return new NotificationRequest
        {
            Title = title,
            Message = message,
            Recipients = await GetRecipients(rule),
            ActionUrl = GetDefaultActionUrl(entity),
            Context = rule.EntityType
        };
    }

    private string RenderSimpleTemplate<T>(string template, T entity)
    {
        var result = template;

        // Replace {PropertyName} với actual values
        foreach (var prop in typeof(T).GetProperties())
        {
            var placeholder = $"{{{prop.Name}}}";
            var value = prop.GetValue(entity)?.ToString() ?? "";
            result = result.Replace(placeholder, value);
        }

        return result;
    }

    private string GetDefaultTitle<T>(SimpleNotificationRule rule, T entity)
    {
        var entityName = GetEntityDisplayName(entity);
        var operatorText = GetOperatorDisplayText(rule.Operator);
        return $"🚨 {entityName}: {rule.FieldName} {operatorText} {rule.Value}";
    }

    private string GetDefaultMessage<T>(SimpleNotificationRule rule, T entity)
    {
        var entityName = GetEntityDisplayName(entity);
        var currentValue = GetFieldValue(entity, rule.FieldName);
        var operatorText = GetOperatorDisplayText(rule.Operator);

        return $"{entityName} có {rule.FieldName} = {currentValue}, {operatorText} ngưỡng {rule.Value}. Cần kiểm tra ngay.";
    }

    private string GetOperatorDisplayText(string @operator)
    {
        return @operator switch
        {
            "LessThan" => "nhỏ hơn",
            "GreaterThan" => "lớn hơn",
            "Equal" => "bằng",
            _ => @operator
        };
    }

    private string GetEntityDisplayName<T>(T entity)
    {
        // Get display name from entity properties
        var nameProperty = typeof(T).GetProperty("CampaignName") ??
                          typeof(T).GetProperty("ProductName");
        return nameProperty?.GetValue(entity)?.ToString() ?? typeof(T).Name;
    }
}
```

---

## 🚨 Notification Trigger Points

### Simple Trigger Service

```csharp
public class SimpleNotificationTriggerService : ITransientDependency
{
    public async Task TriggerCampaignRuleCheck(string campaignId)
    {
        var campaign = await _campaignRepository.GetAsync(campaignId);
        var notifications = await _simpleRuleEngine.EvaluateSimpleRules(new[] { campaign }, "Campaign");

        foreach (var notification in notifications)
        {
            await _notificationService.SendNotificationAsync(notification);
        }
    }

    public async Task TriggerProductRuleCheck(List<string> productIds)
    {
        var products = await _productRepository.GetListAsync(p => productIds.Contains(p.ProductId));
        var notifications = await _simpleRuleEngine.EvaluateSimpleRules(products, "Product");

        foreach (var notification in notifications)
        {
            await _notificationService.SendNotificationAsync(notification);
        }
    }

    public async Task TriggerBulkRuleCheck<T>(IEnumerable<T> entities, string entityType)
    {
        var notifications = await _simpleRuleEngine.EvaluateSimpleRules(entities, entityType);

        foreach (var notification in notifications)
        {
            await _notificationService.SendNotificationAsync(notification);
        }
    }
}
```

### Usage Examples

```csharp
// Trong Dashboard Controller
public async Task<IActionResult> GetDashboard()
{
    var campaigns = await GetRecentCampaigns();

    // Trigger rule check khi user xem dashboard
    await _notificationTrigger.TriggerBulkRuleCheck(campaigns, "Campaign");

    return View(dashboardData);
}

// Trong Data Sync Service
public async Task SyncCampaignData()
{
    var syncedCampaigns = await SyncFromTikTokAPI();

    // Trigger rule check sau khi sync data
    await _notificationTrigger.TriggerBulkRuleCheck(syncedCampaigns, "Campaign");
}
```

---

## 🎨 User Interface Design

### Simple Rule Creation Form

```html
<div class="simple-rule-form">
    <h4>Tạo Quy tắc Thông báo</h4>

    <!-- Step 1: Entity Selection -->
    <div class="form-group">
        <label>Loại dữ liệu:</label>
        <select class="form-select" id="entityType">
            <option value="Campaign">GMV Max Campaign</option>
            <option value="Product">GMV Max Product</option>
        </select>
    </div>

    <!-- Step 2: Field Selection -->
    <div class="form-group">
        <label>Trường dữ liệu:</label>
        <select class="form-select" id="fieldName">
            <option value="ROAS">ROAS (Tỷ lệ lợi nhuận)</option>
            <option value="Cost">Chi phí quảng cáo</option>
            <option value="Orders">Số đơn hàng</option>
            <option value="GrossRevenue">Tổng doanh thu</option>
        </select>
    </div>

    <!-- Step 3: Condition -->
    <div class="form-group">
        <label>Điều kiện:</label>
        <div class="condition-row">
            <select class="form-select" id="operator">
                <option value="LessThan">Nhỏ hơn</option>
                <option value="GreaterThan">Lớn hơn</option>
                <option value="Equal">Bằng</option>
            </select>
            <input
                type="text"
                class="form-control"
                id="value"
                placeholder="Nhập giá trị..."
            />
        </div>
    </div>

    <!-- Step 4: Custom Template (Optional) -->
    <div class="form-group">
        <div class="form-check">
            <input
                type="checkbox"
                class="form-check-input"
                id="useCustomTemplate"
            />
            <label class="form-check-label">Tùy chỉnh nội dung thông báo</label>
        </div>

        <div id="customTemplateSection" style="display: none;">
            <div class="mb-2">
                <label>Tiêu đề:</label>
                <input
                    type="text"
                    class="form-control"
                    id="customTitle"
                    placeholder="🚨 {CampaignName}: ROAS thấp {ROAS}"
                />
            </div>
            <div class="mb-2">
                <label>Nội dung:</label>
                <textarea
                    class="form-control"
                    id="customMessage"
                    rows="2"
                    placeholder="Campaign {CampaignName} có ROAS = {ROAS}, cần kiểm tra ngay"
                ></textarea>
            </div>
            <small class="text-muted">
                Sử dụng {TênTrường} để hiển thị giá trị động. VD:
                {CampaignName}, {ROAS}, {Cost}
            </small>
        </div>
    </div>

    <!-- Save Button -->
    <button type="button" class="btn btn-primary" onclick="saveSimpleRule()">
        Lưu Quy tắc
    </button>
</div>
```

### JavaScript Logic

```javascript
const fieldDefinitions = {
    Campaign: [
        { value: 'ROAS', text: 'ROAS (Tỷ lệ lợi nhuận)', type: 'decimal' },
        { value: 'Cost', text: 'Chi phí quảng cáo', type: 'decimal' },
        { value: 'Orders', text: 'Số đơn hàng', type: 'int' },
        { value: 'GrossRevenue', text: 'Tổng doanh thu', type: 'decimal' },
        { value: 'CampaignName', text: 'Tên Campaign', type: 'string' },
    ],
    Product: [
        { value: 'Orders', text: 'Số đơn hàng', type: 'int' },
        { value: 'GrossRevenue', text: 'Doanh thu', type: 'decimal' },
        { value: 'ProductName', text: 'Tên sản phẩm', type: 'string' },
        {
            value: 'CreativeDeliveryStatus',
            text: 'Trạng thái creative',
            type: 'string',
        },
    ],
};

function saveSimpleRule() {
    const rule = {
        ruleName: `${$('#fieldName option:selected').text()} ${$(
            '#operator option:selected'
        ).text()} ${$('#value').val()}`,
        entityType: $('#entityType').val(),
        fieldName: $('#fieldName').val(),
        operator: $('#operator').val(),
        value: $('#value').val(),
        customTitle: $('#useCustomTemplate').is(':checked')
            ? $('#customTitle').val()
            : null,
        customMessage: $('#useCustomTemplate').is(':checked')
            ? $('#customMessage').val()
            : null,
    };

    $.post('/api/simple-rules', rule).done(() => {
        toastr.success('Quy tắc đã được tạo thành công!');
        $('#createRuleModal').modal('hide');
        loadRulesTable();
    });
}
```

## 📋 Simple Implementation Plan

### Week 1: Database & Backend (5 days)

-   [ ] Create `SimpleNotificationRule` entity và migration
-   [ ] Repository pattern cho SimpleNotificationRule
-   [ ] SimpleRuleEngine service với basic condition evaluation
-   [ ] SimpleTemplateRenderer service
-   [ ] API endpoints: GET, POST, PUT, DELETE cho rules

### Week 2: Frontend & Integration (5 days)

-   [ ] Simple rule creation form với 4 fields
-   [ ] Rules management table với CRUD operations
-   [ ] Field definitions JavaScript object
-   [ ] Integration với existing TikTokNotificationService
-   [ ] Testing với sample campaign/product data

### Week 3: Polish & Deploy (3 days)

-   [ ] Error handling và validation
-   [ ] Default templates cho common scenarios
-   [ ] User testing và feedback
-   [ ] Documentation và deployment

## 📚 Configuration Examples

### Sample Simple Rules

#### 1. Low ROAS Campaign Alert

```json
{
    "ruleName": "ROAS thấp cần chú ý",
    "entityType": "Campaign",
    "fieldName": "ROAS",
    "operator": "LessThan",
    "value": "2.0",
    "customTitle": "🚨 {CampaignName}: ROAS thấp {ROAS}",
    "customMessage": "Campaign {CampaignName} có ROAS = {ROAS} < 2.0. Cần tối ưu ngay!",
    "isActive": true
}
```

#### 2. High Cost No Orders Alert

```json
{
    "ruleName": "Chi phí cao không có đơn",
    "entityType": "Campaign",
    "fieldName": "Orders",
    "operator": "Equal",
    "value": "0",
    "customTitle": null,
    "customMessage": null,
    "isActive": true
}
```

#### 3. Creative Status Problem

```json
{
    "ruleName": "Creative bị từ chối",
    "entityType": "Product",
    "fieldName": "CreativeDeliveryStatus",
    "operator": "Equal",
    "value": "REJECTED",
    "customTitle": "⚠️ {ProductName}: Creative bị từ chối",
    "customMessage": "Sản phẩm {ProductName} có creative bị từ chối. Cần upload creative mới.",
    "isActive": true
}
```

## 🎯 Key Benefits của Simple Version

1. **User-Friendly**: 2-3 phút tạo 1 rule thay vì 45-60 phút
2. **No Learning Curve**: Form đơn giản, không cần training
3. **Fast Development**: 2-3 weeks thay vì 6-8 weeks
4. **Easy Maintenance**: Simple codebase, ít bugs
5. **Extensible**: Có thể thêm features sau này khi cần

## 🎯 Success Criteria

### Technical Metrics

-   [ ] **Performance**: Rule evaluation < 1s per rule per batch
-   [ ] **Scalability**: Support 20+ active rules simultaneously
-   [ ] **Reliability**: 99.5% uptime cho rule evaluation service
-   [ ] **Backward Compatibility**: 0 breaking changes với existing notification system

### Business Metrics

-   [ ] **User Adoption**: 70% users tạo ít nhất 1 rule trong tháng đầu
-   [ ] **Ease of Use**: 90% users có thể tạo rule không cần hỗ trợ
-   [ ] **Time Savings**: Giảm 80% thời gian tạo rule so với manual process
-   [ ] **Notification Relevance**: < 5% complaints về irrelevant notifications

## 🔄 Future Expansion Path

Nếu cần thêm complexity sau này:

### Phase 2: Advanced Features (Optional)

-   [ ] **Multiple Conditions**: AND/OR logic cho complex rules
-   [ ] **More Operators**: Between, Contains, StartsWith, etc.
-   [ ] **All Entity Fields**: Expand từ top 10 lên full field list
-   [ ] **Advanced Templates**: User-type specific templates
-   [ ] **Scheduled Rules**: Tự động check theo schedule

### Phase 3: Enterprise Features (Optional)

-   [ ] **Rule Dependencies**: Rules trigger other rules
-   [ ] **A/B Testing**: Test different notification approaches
-   [ ] **Analytics Dashboard**: Rule performance metrics
-   [ ] **Bulk Operations**: Mass enable/disable rules
-   [ ] **Rule Templates**: Pre-built rule templates for common scenarios

---

_Last Updated: September 22, 2025_  
_Version: 2.0 - Simplified_  
_Status: Ready for Implementation_  
_Timeline: 2-3 weeks_  
_Complexity: Medium (reduced from High)_
