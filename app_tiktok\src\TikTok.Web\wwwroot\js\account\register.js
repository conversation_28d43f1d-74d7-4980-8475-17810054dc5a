// TikTok Register Page JavaScript
document.addEventListener('DOMContentLoaded', function () {
    const registerForm = document.querySelector('form[method="post"]');
    const registerButton = document.querySelector('.btn-login');
    const inputs = document.querySelectorAll('.form-control');

    // Add floating label effect
    inputs.forEach((input) => {
        const label = input.previousElementSibling;

        // Check if input has value on load
        if (input.value.trim() !== '') {
            label.classList.add('floating');
        }

        input.addEventListener('focus', function () {
            label.classList.add('floating', 'focused');
        });

        input.addEventListener('blur', function () {
            label.classList.remove('focused');
            if (input.value.trim() === '') {
                label.classList.remove('floating');
            }
        });

        input.addEventListener('input', function () {
            if (input.value.trim() !== '') {
                label.classList.add('floating');
            } else {
                label.classList.remove('floating');
            }
        });
    });

    // Form submission with loading state
    if (registerForm && registerButton) {
        registerForm.addEventListener('submit', function (e) {
            // Validate form before submission
            if (!validateForm()) {
                e.preventDefault();
                return;
            }

            // Add loading state
            registerButton.classList.add('loading');
            registerButton.disabled = true;

            // Update button text
            const originalText = registerButton.innerHTML;
            registerButton.innerHTML =
                '<i class="fas fa-spinner fa-spin me-2"></i>Đang đăng ký...';

            // If validation fails, restore button state
            setTimeout(() => {
                const hasErrors = document.querySelector(
                    '.text-danger:not(:empty)'
                );
                if (hasErrors) {
                    registerButton.classList.remove('loading');
                    registerButton.disabled = false;
                    registerButton.innerHTML = originalText;
                }
            }, 100);
        });
    }

    // Password visibility toggle for both password fields
    const passwordInputs = document.querySelectorAll('input[type="password"]');
    passwordInputs.forEach((passwordInput) => {
        const toggleButton = document.createElement('button');
        toggleButton.type = 'button';
        toggleButton.className = 'password-toggle';
        toggleButton.innerHTML = '<i class="fas fa-eye"></i>';
        toggleButton.setAttribute('aria-label', 'Toggle password visibility');

        // Ensure parent has relative positioning
        const formGroup = passwordInput.closest('.form-group');
        if (formGroup) {
            formGroup.style.position = 'relative';
            formGroup.appendChild(toggleButton);
        }

        toggleButton.addEventListener('click', function () {
            const isPassword = passwordInput.type === 'password';
            passwordInput.type = isPassword ? 'text' : 'password';
            toggleButton.innerHTML = isPassword
                ? '<i class="fas fa-eye-slash"></i>'
                : '<i class="fas fa-eye"></i>';
        });
    });

    // Add input validation feedback
    inputs.forEach((input) => {
        input.addEventListener('blur', function () {
            validateInput(input);
        });

        input.addEventListener('input', function () {
            // Clear error state on input
            input.classList.remove('error');
            const errorSpan = input.parentNode.querySelector('.text-danger');
            if (errorSpan && !errorSpan.textContent.trim()) {
                errorSpan.style.display = 'none';
            }
        });
    });

    function validateForm() {
        let isValid = true;
        inputs.forEach((input) => {
            if (!validateInput(input)) {
                isValid = false;
            }
        });
        return isValid;
    }

    function validateInput(input) {
        const value = input.value.trim();
        let isValid = true;
        let errorMessage = '';

        // Required field validation
        if (input.hasAttribute('required') && !value) {
            isValid = false;
            errorMessage = 'Trường này là bắt buộc';
        }
        // Email validation
        else if (input.type === 'email' && value && !isValidEmail(value)) {
            isValid = false;
            errorMessage = 'Email không hợp lệ';
        }
        // Username validation
        else if (input.name.includes('UserName') && value) {
            if (value.length < 3) {
                isValid = false;
                errorMessage = 'Tên đăng nhập phải có ít nhất 3 ký tự';
            } else if (!/^[a-zA-Z0-9_]+$/.test(value)) {
                isValid = false;
                errorMessage =
                    'Tên đăng nhập chỉ được chứa chữ, số và dấu gạch dưới';
            }
        }
        // Password validation
        else if (input.name.includes('Password') && value) {
            if (
                input.name.includes('Input.Password') &&
                !input.name.includes('Confirm')
            ) {
                if (value.length < 6) {
                    isValid = false;
                    errorMessage = 'Mật khẩu phải có ít nhất 6 ký tự';
                } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
                    isValid = false;
                    errorMessage =
                        'Mật khẩu phải có ít nhất 1 chữ hoa, 1 chữ thường và 1 số';
                }
            }
            // Confirm password validation
            else if (input.name.includes('ConfirmPassword')) {
                const passwordInput = document.querySelector(
                    'input[name="Input.Password"]'
                );
                if (passwordInput && value !== passwordInput.value) {
                    isValid = false;
                    errorMessage = 'Mật khẩu xác nhận không khớp';
                }
            }
        }

        if (!isValid) {
            input.classList.add('error');
            showError(input, errorMessage);
        } else {
            input.classList.remove('error');
            hideError(input);
        }

        return isValid;
    }

    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    function showError(input, message) {
        let errorSpan = input.parentNode.querySelector('.text-danger');
        if (errorSpan) {
            errorSpan.textContent = message;
            errorSpan.style.display = 'block';
        }
    }

    function hideError(input) {
        const errorSpan = input.parentNode.querySelector('.text-danger');
        if (errorSpan && !errorSpan.textContent.trim()) {
            errorSpan.style.display = 'none';
        }
    }

    // Add smooth scroll to error
    const firstError = document.querySelector('.text-danger:not(:empty)');
    if (firstError) {
        firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    // Add ripple effect to buttons
    document
        .querySelectorAll('.btn-login, .btn-outline-secondary')
        .forEach((button) => {
            button.addEventListener('click', function (e) {
                const ripple = document.createElement('span');
                const rect = button.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.className = 'ripple';
                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';

                button.appendChild(ripple);

                setTimeout(() => ripple.remove(), 600);
            });
        });

    // Real-time password confirmation validation
    const confirmPasswordInput = document.querySelector(
        'input[name="Input.ConfirmPassword"]'
    );
    const passwordInput = document.querySelector(
        'input[name="Input.Password"]'
    );

    if (confirmPasswordInput && passwordInput) {
        [confirmPasswordInput, passwordInput].forEach((input) => {
            input.addEventListener('input', function () {
                if (confirmPasswordInput.value && passwordInput.value) {
                    validateInput(confirmPasswordInput);
                }
            });
        });
    }
});

// Add keyboard navigation support
document.addEventListener('keydown', function (e) {
    if (e.key === 'Enter' && e.target.matches('.form-control')) {
        const form = e.target.closest('form');
        const inputs = form.querySelectorAll('.form-control');
        const currentIndex = Array.from(inputs).indexOf(e.target);

        if (currentIndex < inputs.length - 1) {
            e.preventDefault();
            inputs[currentIndex + 1].focus();
        }
    }
});
