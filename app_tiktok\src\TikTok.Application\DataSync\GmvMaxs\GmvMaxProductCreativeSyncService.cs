using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TikTok.Application.Contracts.Notification;
using TikTok.Application.Notification;
using TikTok.Consts;
using TikTok.DataSync.Services;
using TikTok.DateTimes;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Repositories;
using TikTok.TikTokApiClients;
using TikTokBusinessApi;
using TikTokBusinessApi.Models;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations;
using Volo.Abp.Uow;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service implementation cho việc đồng bộ dữ liệu báo cáo chi tiết cấp creative GMV Max Product Campaign
    /// </summary>
    public class GmvMaxProductCreativeSyncService : BaseSyncService, IGmvMaxProductCreativeSyncService
    {
        private readonly IRawGmvMaxProductCreativeReportRepository _gmvMaxProductCreativeReportRepository;
        private readonly IRawGmvMaxProductCreativeReportDapperRepository _gmvMaxProductCreativeReportDapperRepository;
        private readonly IAssetRepository _assetRepository;
        private readonly IRawGmvMaxCampaignsRepository _gmvMaxCampaignsRepository;
        private readonly ITikTokApiClientService _tikTokApiClientService;
        private readonly IApiMetricsService _apiMetricsService;
        private const string SERVICE_NAME = "GmvMaxProductCreativeSyncService";
        private readonly ITikTokNotificationService _tikTokNotificationService;
        private readonly CreativeStatusChangeTracker _statusChangeTracker;

        public GmvMaxProductCreativeSyncService(
            IServiceProvider serviceProvider,
            IRawGmvMaxProductCreativeReportRepository gmvMaxProductCreativeReportRepository,
            IRawGmvMaxProductCreativeReportDapperRepository gmvMaxProductCreativeReportDapperRepository,
            ILogger<GmvMaxProductCreativeSyncService> logger,
            IAssetRepository assetRepository,
            IRawGmvMaxCampaignsRepository gmvMaxCampaignsRepository,
            IApiMetricsService apiMetricsService,
            ITikTokApiClientService tikTokApiClientService,
            ITikTokNotificationService tikTokNotificationService,
            CreativeStatusChangeTracker statusChangeTracker) : base(serviceProvider, logger)
        {
            _gmvMaxProductCreativeReportRepository = gmvMaxProductCreativeReportRepository;
            _gmvMaxProductCreativeReportDapperRepository = gmvMaxProductCreativeReportDapperRepository;
            _assetRepository = assetRepository;
            _gmvMaxCampaignsRepository = gmvMaxCampaignsRepository;
            _tikTokApiClientService = tikTokApiClientService;
            _apiMetricsService = apiMetricsService;
            _tikTokNotificationService = tikTokNotificationService;
            _statusChangeTracker = statusChangeTracker;
        }

        /// <summary>
        /// Đồng bộ báo cáo chi tiết cấp creative GMV Max Product Campaign cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxProductCreativeSyncResult> SyncAllGmvMaxProductCreativeForAllBcsAsync()
        {
            // Bắt đầu tracking metrics
            _apiMetricsService.StartTracking(SERVICE_NAME);

            var result = new GmvMaxProductCreativeSyncResult
            {
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo chi tiết cấp creative GMV Max Product Campaign cho tất cả BC");

                var allBcs = await _businessApplicationCache.GetAllActiveAsync();
                var bcIds = allBcs.Select(x => x.BcId).Distinct().ToList();

                var totalResult = new GmvMaxProductCreativeSyncResult
                {
                };

                foreach (var bcId in bcIds)
                {
                    var bcResult = await SyncGmvMaxProductCreativeAsync(bcId);
                    totalResult.TotalSynced += bcResult.TotalSynced;
                    totalResult.NewRecords += bcResult.NewRecords;
                    totalResult.UpdatedRecords += bcResult.UpdatedRecords;
                    totalResult.SkippedRecords += bcResult.SkippedRecords;
                    totalResult.DayCount += bcResult.DayCount;
                    totalResult.CampaignCount += bcResult.CampaignCount;
                    totalResult.StoreCount += bcResult.StoreCount;
                    totalResult.ProductCount += bcResult.ProductCount;
                    totalResult.CreativeCount += bcResult.CreativeCount;
                    totalResult.BcCount++;
                }

                result = totalResult;

                _logger.LogDebug("Hoàn thành đồng bộ báo cáo chi tiết cấp creative GMV Max Product Campaign cho tất cả BC. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, Bỏ qua: {Skipped}, BC: {BcCount}, Campaign: {CampaignCount}, Store: {StoreCount}, Product: {ProductCount}, Creative: {CreativeCount}, Ngày: {DayCount}",
                    result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.SkippedRecords, result.BcCount, result.CampaignCount, result.StoreCount, result.ProductCount, result.CreativeCount, result.DayCount);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo chi tiết cấp creative GMV Max Product Campaign cho tất cả BC");
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ báo cáo chi tiết cấp creative GMV Max Product Campaign: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo chi tiết cấp creative GMV Max Product Campaign cho tất cả BC");
            }
            finally
            {
                // Luôn lấy metrics trước khi return, kể cả khi có exception
                result.ApiMetrics = _apiMetricsService.GetMetrics(SERVICE_NAME);
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ báo cáo chi tiết cấp creative GMV Max Product Campaign theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxProductCreativeSyncResult> SyncGmvMaxProductCreativeAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null)
        {
            // Bắt đầu tracking metrics nếu chưa có
            _apiMetricsService.StartTracking(SERVICE_NAME, bcId);

            var result = new GmvMaxProductCreativeSyncResult
            {
                BcCount = 1
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo chi tiết cấp creative GMV Max Product Campaign cho BC: {BcId}", bcId);

                // Tạo TikTok client từ Singleton
                var tikTokClient = await _tikTokApiClientService.GetOrCreateClientAsync(bcId);

                string bcTimezone = DateTimeService.UTC_TIMEZONE;

                var bc = await _businessCenterCache.GetByBcIdAsync(bcId);
                if (bc != null && !string.IsNullOrEmpty(bc.Timezone))
                {
                    bcTimezone = bc.Timezone;
                }

                // ✅ Fixed N+1 Query Problem: Single query thay vì N+1 queries
                var allGmvMaxCampaigns = await _gmvMaxCampaignsRepository.GetByBcIdAsync(bcId);
                var advertiserGroups = allGmvMaxCampaigns.GroupBy(x => x.AdvertiserId).ToList();

                // ✅ Pre-populate campaign name cache để tránh future N+1 queries
                foreach (var campaign in allGmvMaxCampaigns)
                {
                    _campaignNameCache[campaign.CampaignId] = campaign.CampaignName;
                }

                _logger.LogDebug("Tìm thấy {AdvertiserCount} advertisers với {CampaignCount} campaigns cho BC: {BcId}. Cached {CacheCount} campaign names.",
                    advertiserGroups.Count, allGmvMaxCampaigns.Count, bcId, _campaignNameCache.Count);
                DateTime currentDateInTimezone = _dateTimeService.GetDateNow(bcTimezone);

                // Duyệt từng nhóm advertiser
                foreach (var advertiserGroup in advertiserGroups)
                {
                    var advertiserId = advertiserGroup.Key;
                    var advertiserCampaigns = advertiserGroup.ToList();

                    _logger.LogDebug("Xử lý advertiser: {AdvertiserId} với {CampaignCount} campaigns",
                        advertiserId, advertiserCampaigns.Count);

                    (DateTime startDate, DateTime endDate) rangeDateFiltering;
                    if (startDate.HasValue && endDate.HasValue)
                    {
                        if (startDate.Value > endDate.Value)
                        {
                            throw new UserFriendlyException("Ngày bắt đầu phải nhỏ hơn ngày kết thúc");
                        }
                        rangeDateFiltering = (startDate.Value, endDate.Value);
                    }
                    else
                    {
                        rangeDateFiltering = await GetRangeDateFiltering(bcId, advertiserId, bcTimezone);
                    }

                    var currentDate = rangeDateFiltering.startDate.Date;
                    var end = rangeDateFiltering.endDate.Date;

                    while (currentDate <= end)
                    {
                        _logger.LogDebug("Đồng bộ dữ liệu báo cáo chi tiết cấp creative GMV Max Product Campaign cho Advertiser: {AdvertiserId}, Ngày: {Date}", advertiserId, currentDate.ToString("yyyy-MM-dd"));

                        var apiResponse = await GetSyncGmvMaxProductCreativeFromApiAsync(tikTokClient, bcId, advertiserId, currentDate, currentDate, advertiserCampaigns);
                        if (apiResponse != null && apiResponse.Any())
                        {
                            await ProcessReportDataAsync(bcId, advertiserId, bcTimezone, apiResponse, result, currentDate, startDate.HasValue && endDate.HasValue && startDate.Value.Date == endDate.Value.Date && endDate.Value.Date == currentDateInTimezone.Date);
                        }
                        else
                        {
                            _logger.LogDebug("Không có dữ liệu báo cáo chi tiết cấp creative GMV Max Product Campaign cho Advertiser: {AdvertiserId}, Ngày: {Date}", advertiserId, currentDate.ToString("yyyy-MM-dd"));
                        }
                        currentDate = currentDate.AddDays(1);
                    }
                }

                _logger.LogDebug("Hoàn thành đồng bộ báo cáo chi tiết cấp creative GMV Max Product Campaign cho BC: {BcId}. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, Campaign: {CampaignCount}, Store: {StoreCount}, Product: {ProductCount}, Creative: {CreativeCount}, Ngày: {DayCount}",
                    bcId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.CampaignCount, result.StoreCount, result.ProductCount, result.CreativeCount, result.DayCount);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo chi tiết cấp creative GMV Max Product Campaign cho BC: {BcId}", bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ báo cáo chi tiết cấp creative GMV Max Product Campaign: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo chi tiết cấp creative GMV Max Product Campaign cho BC: {BcId}", bcId);
            }
            finally
            {
                // ✅ RELIABILITY FIX: Luôn gửi thông báo trước khi clear cache, kể cả khi có exception
                try
                {
                    await ProcessStatusChangeNotificationsAsync();
                    _logger.LogDebug("Successfully processed status change notifications in finally block");
                }
                catch (Exception notificationEx)
                {
                    // ✅ SAFETY: Lỗi thông báo không ảnh hưởng đến sync result
                    _logger.LogError(notificationEx, "Lỗi khi gửi thông báo status changes, nhưng sync data vẫn thành công cho BC: {BcId}", bcId);
                    // ✅ Không throw - để sync result được return bình thường
                }

                // Luôn lấy metrics trước khi return, kể cả khi có exception
                result.ApiMetrics = _apiMetricsService.GetMetrics(SERVICE_NAME, bcId);

                // ✅ Clear cache sau mỗi BC để tránh memory leak (SAU KHI đã gửi thông báo)
                _campaignNameCache.Clear();

                // ✅ Clear StringBuilder để tránh memory leak
                _errorMessageBuilder.Clear();
            }

            return result;
        }

        private async Task<(DateTime startDate, DateTime endDate)> GetRangeDateFiltering(string bcId, string advertiserId, string timezone)
        {
            // Lấy ngày hiện tại theo timezone của BC
            DateTime currentDateInTimezone = _dateTimeService.GetDateNow(timezone);
            DateTime endDate = currentDateInTimezone;

            // Lấy dữ liệu báo cáo chi tiết cấp creative GMV Max Product Campaign mới nhất (theo giờ)
            //var latestReport = await GetLatestReportByBcIdAsync(bcId, advertiserId);

            DateTime startDate = currentDateInTimezone;
            //if (latestReport == null)
            //{
            //    // Nếu chưa có dữ liệu trong DB thì lấy khoảng 1 tuần từ ngày hiện tại
            //    startDate = currentDateInTimezone.AddDays(-LAST_SYNC_DAYS);
            //    _logger.LogDebug("Chưa có dữ liệu báo cáo chi tiết cấp creative GMV Max Product Campaign trong DB cho BC: {BcId}, Advertiser: {AdvertiserId}. Lấy dữ liệu 1 tuần từ {StartDate} đến {EndDate}",
            //        bcId, advertiserId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));
            //}
            //else
            //{
            //    // Nếu có dữ liệu trong DB thì lấy từ ngày trong DB đến ngày hiện tại
            //    // Convert từ UTC (trong DB) sang timezone của BC để so sánh
            //    var latestReportDateInTimezone = _dateTimeService.ConvertFromUtc(latestReport.Date, timezone).Date;
            //    startDate = latestReportDateInTimezone;
            //    _logger.LogDebug("Có dữ liệu báo cáo chi tiết cấp creative GMV Max Product Campaign trong DB cho BC: {BcId}, Advertiser: {AdvertiserId}. Lấy từ {StartDate} đến {EndDate}",
            //        bcId, advertiserId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));
            //}

            return (startDate, endDate);
        }

        /// <summary>
        /// Lấy báo cáo mới nhất theo BC ID và advertiserId
        /// </summary>
        /// <param name="bcId">BC ID</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <returns>Báo cáo mới nhất</returns>
        private async Task<RawGmvMaxProductCreativeReportEntity?> GetLatestReportByBcIdAsync(string bcId, string advertiserId)
        {
            var query = await _gmvMaxProductCreativeReportRepository.GetLatestByBcIdAndAdvertiserIdAsync(bcId, advertiserId);

            return query;
        }

        /// <summary>
        /// Lấy dữ liệu báo cáo chi tiết cấp creative GMV Max Product Campaign từ TikTok API
        /// Phân trang theo campaign ID và item group ID: 100 CampaignId và 10 ItemGroupId mỗi lần gọi API
        /// </summary>
        /// <param name="advertiserCampaigns">Campaigns đã được load từ database (tránh N+1 query)</param>
        private async Task<List<GmvMaxProductCreativeReportData>> GetSyncGmvMaxProductCreativeFromApiAsync(TikTokBusinessApiClient tikTokClient, string bcId, string advertiserId, DateTime startDate, DateTime endDate, List<RawGmvMaxCampaignsEntity> advertiserCampaigns = null)
        {
            var records = new List<GmvMaxProductCreativeReportData>();

            // ✅ Performance Fix: Sử dụng dữ liệu đã load thay vì query lại database
            var gmvMaxCampaigns = advertiserCampaigns ?? await _gmvMaxCampaignsRepository.GetByAdvertiserIdAsync(advertiserId);
            var storeItemGroupMapping = gmvMaxCampaigns
                .Where(x => !string.IsNullOrEmpty(x.StoreId) && x.ItemGroupIds != null)
                .GroupBy(x => new { x.StoreId, x.CampaignId })
                .Select(g => new
                {
                    g.Key.StoreId,
                    g.Key.CampaignId,
                    ItemGroupIds = g.SelectMany(x => x.ItemGroupIds).Distinct().ToList()
                })
                .ToList();

            if (!storeItemGroupMapping.Any())
            {
                _logger.LogDebug("Không có Store và ItemGroup ID nào cho Advertiser: {AdvertiserId}", advertiserId);
                return records;
            }

            _logger.LogDebug("Tìm thấy {Count} Store-ItemGroup mapping cho Advertiser: {AdvertiserId}", storeItemGroupMapping.Count, advertiserId);

            // Tạo danh sách tất cả campaign IDs và item group IDs
            var allStoreIds = storeItemGroupMapping.Select(x => x.StoreId).Distinct().ToList();

            // Phân trang theo store để tránh trùng lặp dữ liệu
            // Mỗi store sẽ có các campaign và item group riêng
            foreach (var storeId in allStoreIds)
            {
                // Lấy các campaign thuộc store này
                var campaignsForStore = storeItemGroupMapping
                    .Where(x => x.StoreId == storeId)
                    .Select(x => x.CampaignId)
                    .Distinct()
                    .ToList();

                // Lấy tất cả item group IDs cho store này
                var itemGroupsForStore = storeItemGroupMapping
                    .Where(x => x.StoreId == storeId)
                    .SelectMany(x => x.ItemGroupIds)
                    .Distinct()
                    .ToList();

                // Phân trang theo campaign ID (100 campaigns mỗi batch)
                const int campaignBatchSize = 100;
                const int itemGroupBatchSize = 100;

                for (int campaignBatchIndex = 0; campaignBatchIndex < campaignsForStore.Count; campaignBatchIndex += campaignBatchSize)
                {
                    var campaignBatch = campaignsForStore.Skip(campaignBatchIndex).Take(campaignBatchSize).ToList();

                    // Phân trang theo item group ID (10 item groups mỗi batch)
                    for (int itemGroupBatchIndex = 0; itemGroupBatchIndex < itemGroupsForStore.Count; itemGroupBatchIndex += itemGroupBatchSize)
                    {
                        var itemGroupBatch = itemGroupsForStore.Skip(itemGroupBatchIndex).Take(itemGroupBatchSize).ToList();

                        // Gọi API với batch hiện tại cho store cụ thể
                        var batchRecords = await GetReportFromApiBatchAsync(tikTokClient, bcId, advertiserId, new List<string> { storeId }, campaignBatch, itemGroupBatch, startDate, endDate);
                        records.AddRange(batchRecords);

                        _logger.LogDebug("Đã lấy {Count} records cho Store: {StoreId}, Campaign IDs: {CampaignCount}, ItemGroup IDs: {ItemGroupCount}",
                            batchRecords.Count, storeId, campaignBatch.Count, itemGroupBatch.Count);
                    }
                }
            }

            return records;
        }

        /// <summary>
        /// Lấy báo cáo từ API theo batch campaign IDs và item group IDs
        /// </summary>
        private async Task<List<GmvMaxProductCreativeReportData>> GetReportFromApiBatchAsync(TikTokBusinessApiClient tikTokClient, string bcId, string advertiserId, List<string> storeIds, List<string> campaignIds, List<string> itemGroupIds, DateTime startDate, DateTime endDate,  string creativeType = null)
        {
            var records = new List<GmvMaxProductCreativeReportData>();
            var page = 1;
            const int pageSize = PAGE_SIZE_SYNC_REPORT;

            var filtering = new GMVMaxReportFiltering
            {
                CampaignIds = campaignIds,
                ItemGroupIds = itemGroupIds,
            };
            if (!creativeType.IsNullOrWhiteSpace())
            {
                filtering.CreativeTypes = new List<string> { creativeType };
            }

            while (true)
            {
                var request = new GMVMaxReportRequest
                {
                    AdvertiserId = advertiserId,
                    StoreIds = storeIds,
                    StartDate = startDate.ToString("yyyy-MM-dd"),
                    EndDate = endDate.ToString("yyyy-MM-dd"),
                    Dimensions = new List<string> { "campaign_id", "item_group_id", "item_id" },
                    Metrics = new List<string>
                    {
                         "currency", "creative_delivery_status",
                        "orders", "gross_revenue", "product_impressions", "product_clicks",
                        "product_click_rate", "ad_click_rate", "ad_conversion_rate",
                        "ad_video_view_rate_2s", "ad_video_view_rate_6s", "ad_video_view_rate_p25",
                        "ad_video_view_rate_p50", "ad_video_view_rate_p75", "ad_video_view_rate_p100",
                        "roi", "cost_per_order", "cost"
                    },
                    Filtering = filtering,
                    Page = page,
                    PageSize = pageSize
                };

                // Ghi nhận API call và kiểm tra giới hạn
                if (!_apiMetricsService.RecordApiCall(SERVICE_NAME, bcId))
                {
                    throw new BusinessException($"Đã vượt giới hạn API calls cho service {SERVICE_NAME} (BC: {bcId}) trong môi trường Development");
                }

                var response = await tikTokClient.GMVMax.GetReportAsync(request);
                if (!TikTokApiCodes.IsSuccess(response.Code))
                {
                    throw new BusinessException(response.Code.ToString(), $"Lỗi khi lấy dữ liệu báo cáo chi tiết cấp creative GMV Max Product Campaign cho CreativeType {creativeType}: {response.Message}");
                }

                if (response?.Data?.List == null || !response.Data.List.Any())
                {
                    break;
                }

                // Lấy campaignId, itemGroupId và itemId từ dimensions của response
                foreach (var item in response.Data.List)
                {
                    var itemId = item.Dimensions?.GetValueOrDefault("item_id")?.ToString();
                    var campaignId = item.Dimensions?.GetValueOrDefault("campaign_id")?.ToString();
                    var itemGroupId = item.Dimensions?.GetValueOrDefault("item_group_id")?.ToString();

                    // Kiểm tra các ID có hợp lệ không (không null, rỗng)
                    // Lưu ý: itemId có thể là "-1" (sẽ được xử lý đặc biệt với ShopContentType = PRODUCT_CARD)
                    if (string.IsNullOrEmpty(itemId) ||
                        string.IsNullOrEmpty(campaignId) || campaignId == "-1" ||
                        string.IsNullOrEmpty(itemGroupId) || itemGroupId == "-1")
                    {
                        _logger.LogWarning("ID không hợp lệ - ItemId: {ItemId}, CampaignId: {CampaignId}, ItemGroupId: {ItemGroupId}",
                            itemId, campaignId, itemGroupId);
                        continue;
                    }


                    // Vì chúng ta gọi API với 1 store cụ thể, sử dụng store đầu tiên
                    var storeId = storeIds.FirstOrDefault() ?? "";

                    // Tạo record với các ID từ dimensions
                    records.Add(new GmvMaxProductCreativeReportData(item, advertiserId, storeId, campaignId, itemGroupId));
                }
                // Kiểm tra xem còn trang tiếp theo không
                if (response.Data.PageInfo?.TotalPage <= page)
                {
                    break;
                }

                page++;
            }

            _logger.LogDebug("Lấy được {Count} records cho batch CreativeType {CreativeType}, CampaignIds: {CampaignCount}, ItemGroupIds: {ItemGroupCount}",
                records.Count, creativeType, campaignIds.Count, itemGroupIds.Count);

            return records;
        }

        /// <summary>
        /// Lấy báo cáo từ API theo creative type (phương thức cũ - giữ lại để tương thích)
        /// </summary>
        private async Task<List<GmvMaxProductCreativeReportData>> GetReportFromApiAsync(TikTokBusinessApiClient tikTokClient, string bcId, string advertiserId, string storeId, string campaignId, string itemGroupId, DateTime startDate, DateTime endDate, string creativeType = null)
        {
            var records = new List<GmvMaxProductCreativeReportData>();
            var page = 1;
            const int pageSize = PAGE_SIZE_SYNC_REPORT;

            var filtering = new GMVMaxReportFiltering
            {
                CampaignIds = new List<string> { campaignId },
                ItemGroupIds = new List<string> { itemGroupId }, // Từ campaign data
            };
            if (!creativeType.IsNullOrWhiteSpace())
            {
                filtering.CreativeTypes = new List<string> { creativeType };
            }
            while (true)
            {
                var request = new GMVMaxReportRequest
                {
                    AdvertiserId = advertiserId,
                    StoreIds = new List<string> { storeId },
                    StartDate = startDate.ToString("yyyy-MM-dd"), // Lấy dữ liệu 1 ngày trước
                    EndDate = endDate.ToString("yyyy-MM-dd"),
                    Dimensions = new List<string> { "item_id" }, // Chỉ item_id
                    Metrics = new List<string>
                    {
                        "currency", "title", "tt_account_name", "tt_account_profile_image_url",
                        "tt_account_authorization_type", "shop_content_type","creative_delivery_status",
                        "orders", "gross_revenue", "product_impressions", "product_clicks",
                        "product_click_rate", "ad_click_rate", "ad_conversion_rate",
                        "ad_video_view_rate_2s", "ad_video_view_rate_6s", "ad_video_view_rate_p25",
                        "ad_video_view_rate_p50", "ad_video_view_rate_p75", "ad_video_view_rate_p100",
                        "roi", "cost_per_order", "cost"
                    },
                    Filtering = filtering,
                    Page = page,
                    PageSize = pageSize
                };

                // Ghi nhận API call và kiểm tra giới hạn
                if (!_apiMetricsService.RecordApiCall(SERVICE_NAME, bcId))
                {
                    throw new BusinessException($"Đã vượt giới hạn API calls cho service {SERVICE_NAME} (BC: {bcId}) trong môi trường Development");
                }

                var response = await tikTokClient.GMVMax.GetReportAsync(request);
                if (!TikTokApiCodes.IsSuccess(response.Code))
                {
                    throw new BusinessException(response.Code.ToString(), $"Lỗi khi lấy dữ liệu báo cáo chi tiết cấp creative GMV Max Product Campaign cho CreativeType {creativeType}: {response.Message}");
                }

                if (response?.Data?.List == null || !response.Data.List.Any())
                {
                    break;
                }

                // Thêm AdvertiserId, StoreId và CampaignId vào mỗi item
                foreach (var item in response.Data.List)
                {
                    records.Add(new GmvMaxProductCreativeReportData(item, advertiserId, storeId, campaignId, itemGroupId));
                }

                // Kiểm tra xem còn trang tiếp theo không
                if (response.Data.PageInfo?.TotalPage <= page)
                {
                    break;
                }

                page++;
            }

            _logger.LogDebug("Lấy được {Count} records cho CreativeType {CreativeType}, ItemGroupId {ItemGroupId}, CampaignId {CampaignId}",
                records.Count, creativeType, itemGroupId, campaignId);

            return records;
        }

        /// <summary>
        /// Xử lý dữ liệu báo cáo từ API
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="advertiserId">ID nhà quảng cáo</param>
        /// <param name="bcTimezone">Timezone của Business Center</param>
        /// <param name="reportDataList">Danh sách dữ liệu báo cáo</param>
        /// <param name="result">Kết quả đồng bộ</param>
        /// <param name="syncDate">Ngày đồng bộ</param>
        private async Task ProcessReportDataAsync(string bcId, string advertiserId, string bcTimezone, List<GmvMaxProductCreativeReportData> reportDataList, GmvMaxProductCreativeSyncResult result, DateTime syncDate, bool pushNotiForStatusChange)
        {
            // sử dụng UnitOfWork để đảm bảo tính toàn vẹn dữ liệu cho mỗi 200 bản ghi
            if (reportDataList == null || !reportDataList.Any())
            {
                _logger.LogDebug("Không có dữ liệu báo cáo chi tiết cấp creative GMV Max Product Campaign để xử lý cho {Total} bản ghi", reportDataList?.Count ?? 0);
                return;
            }

            var pageSize = PAGE_SIZE_HANDLE_SAVE_TO_DATABASE;
            var totalPages = (int)Math.Ceiling((double)reportDataList.Count / pageSize);
            _logger.LogDebug("Bắt đầu xử lý {TotalRecords} bản ghi báo cáo chi tiết cấp creative GMV Max Product Campaign cho {TotalPages} trang", reportDataList.Count, totalPages);

            // ✅ PERFORMANCE OPTIMIZATION: Batch load tất cả mapped entities TRƯỚC KHI process pages
            var allMappedEntities = await MapListReportDataToEntitiesAsync(bcId, bcTimezone, reportDataList, syncDate);
            var existingEntitiesLookup = await BatchLoadExistingEntitiesAsync(bcId, allMappedEntities);

            _logger.LogDebug("Batch loaded {ExistingCount} existing entities for {TotalRecords} records",
                existingEntitiesLookup.Count, reportDataList.Count);

            for (int page = 0; page < totalPages; page++)
            {
                var pageData = reportDataList.Skip(page * pageSize).Take(pageSize).ToList();
                if (pageData.Any())
                {
                    try
                    {
                        // ✅ Pass pre-loaded entities lookup to avoid per-page queries
                        var pageMappedEntities = allMappedEntities.Skip(page * pageSize).Take(pageSize).ToList();
                        await ProcessPageDataWithPreloadedEntitiesAsync(bcId, bcTimezone, pageData, pageMappedEntities, existingEntitiesLookup, result, syncDate, pushNotiForStatusChange);
                    }
                    catch (BusinessException ex)
                    {
                        _logger.LogError(ex, "Lỗi khi xử lý dữ liệu báo cáo chi tiết cấp creative GMV Max Product Campaign cho {Total} bản ghi, Trang: {Page}", pageData.Count, page + 1);

                        // ✅ FIXED: Sử dụng StringBuilder thay vì O(n²) string concatenation
                        AppendErrorMessage(result, ex.Message);

                        result.Code = TikTokApiCodes.PartialSuccess.ToString();
                        continue; // Bỏ qua lỗi và tiếp tục với trang tiếp theo
                    }
                }
            }

            // ✅ REMOVED: Notification processing moved to finally block để đảm bảo reliability
            // await ProcessStatusChangeNotificationsAsync(); // Moved to finally block
        }

        /// <summary>
        /// Process page data với pre-loaded existing entities để tránh per-page queries
        /// </summary>
        private async Task ProcessPageDataWithPreloadedEntitiesAsync(
            string bcId,
            string bcTimezone,
            List<GmvMaxProductCreativeReportData> pageData,
            List<RawGmvMaxProductCreativeReportEntity> pageMappedEntities,
            Dictionary<string, RawGmvMaxProductCreativeReportEntity> existingEntitiesLookup,
            GmvMaxProductCreativeSyncResult result,
            DateTime syncDate,
            bool pushNotiForStatusChange)
        {
            // use unit of work
            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false))
            {
                // ✅ Sử dụng pre-loaded entities thay vì query database
                // No database call needed here!

                var insertedEntities = new List<RawGmvMaxProductCreativeReportEntity>();
                var updatedEntities = new List<RawGmvMaxProductCreativeReportEntity>();

                // ✅ Dictionary để track inserted entities theo key để tránh duplicate trong cùng page
                var insertedEntitiesLookup = new Dictionary<string, RawGmvMaxProductCreativeReportEntity>();

                // ✅ Collect status changes để process batch sau (tránh async trong loop)
                var statusChanges = new List<(RawGmvMaxProductCreativeReportEntity currentEntity, RawGmvMaxProductCreativeReportEntity mappedEntity)>();

                foreach (var mappedEntity in pageMappedEntities)
                {
                    // ✅ Kiểm tra metrics: nếu tất cả metrics đều là 0 thì bỏ qua
                    if (mappedEntity.AreAllMetricsZero())
                    {
                        // Tìm bản ghi mới nhất cho đến ngày của mappedEntity để so sánh
                        var latestEntity = await _gmvMaxProductCreativeReportDapperRepository.GetLatestByKeyUntilDateAsync(
                            mappedEntity.BcId,
                            mappedEntity.AdvertiserId,
                            mappedEntity.CampaignId,
                            mappedEntity.ItemGroupId,
                            mappedEntity.ItemId,
                            mappedEntity.StoreId,
                            mappedEntity.Date);

                        // Nếu bản ghi mới nhất cũng có tất cả metrics là 0 thì bỏ qua
                        if (latestEntity != null && latestEntity.AreAllMetricsZero())
                        {
                            result.SkippedRecords++;
                            continue;
                        }
                    }

                    // ✅ O(1) dictionary lookup với pre-loaded entities
                    var lookupKey = $"{mappedEntity.BcId}|{mappedEntity.AdvertiserId}|{mappedEntity.CampaignId}|{mappedEntity.ItemGroupId}|{mappedEntity.ItemId}|{mappedEntity.StoreId}|{mappedEntity.Date.Date:yyyy-MM-dd}";

                    if (!existingEntitiesLookup.TryGetValue(lookupKey, out var currentEntity))
                    {
                        // ✅ Kiểm tra duplicate trong insertedEntities list
                        if (!insertedEntitiesLookup.ContainsKey(lookupKey))
                        {
                            insertedEntities.Add(mappedEntity);
                            insertedEntitiesLookup[lookupKey] = mappedEntity;
                            result.NewRecords++;
                        }
                        else
                        {
                            // Entity đã tồn tại trong insertedEntities, bỏ qua
                            result.SkippedRecords++;
                        }
                    }
                    else
                    {
                        // Cập nhật nếu có thay đổi thì tạo mới
                        if (currentEntity.IsDifferentFrom(mappedEntity, false))
                        {
                            currentEntity.UpdateFromNewData(mappedEntity);
                            updatedEntities.Add(currentEntity);
                            result.UpdatedRecords++;
                            result.TotalSynced++;

                            // ✅ Collect status changes thay vì process ngay (NO ASYNC!)
                            if (currentEntity.StatusChanged(mappedEntity, false)&& pushNotiForStatusChange)
                            {
                                statusChanges.Add((currentEntity, mappedEntity));
                            }
                        }
                    }
                }

                // ✅ Process status changes in batch AFTER loop (single async operation)
                if (statusChanges.Any())
                {
                    await ProcessStatusChangesBatchAsync(statusChanges);
                }

                // Thêm các bản ghi mới vào kho dữ liệu
                if (insertedEntities.Any())
                {
                    await _gmvMaxProductCreativeReportRepository.InsertManyAsync(insertedEntities);
                }
                // Cập nhật các bản ghi đã tồn tại
                if (updatedEntities.Any())
                {
                    await _gmvMaxProductCreativeReportRepository.UpdateManyAsync(updatedEntities);
                }

                // ✅ Performance Fix: Pre-calculate counts để tránh multiple iterations
                result.DayCount += pageData.Count;
                result.CampaignCount += pageData.Select(x => x.CampaignId).Distinct().Count();
                result.StoreCount += pageData.Select(x => x.StoreId).Distinct().Count();

                // ✅ Single iteration với HashSet để tính distinct counts
                var itemGroupIds = new HashSet<string>();
                var itemIds = new HashSet<string>();

                foreach (var item in pageData)
                {
                    var itemGroupId = item.ReportItem.Dimensions?.GetValueOrDefault("item_group_id")?.ToString();
                    var itemId = item.ReportItem.Dimensions?.GetValueOrDefault("item_id")?.ToString();

                    if (!string.IsNullOrEmpty(itemGroupId)) itemGroupIds.Add(itemGroupId);
                    if (!string.IsNullOrEmpty(itemId)) itemIds.Add(itemId);
                }

                result.ProductCount += itemGroupIds.Count;
                result.CreativeCount += itemIds.Count;

                await uow.CompleteAsync();
            }
        }

        /// <summary>
        /// Batch load tất cả existing entities để tránh 10K queries
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="allMappedEntities">Tất cả mapped entities từ tất cả pages</param>
        /// <returns>Dictionary lookup cho existing entities</returns>
        private async Task<Dictionary<string, RawGmvMaxProductCreativeReportEntity>> BatchLoadExistingEntitiesAsync(string bcId, List<RawGmvMaxProductCreativeReportEntity> allMappedEntities)
        {
            if (!allMappedEntities.Any())
                return new Dictionary<string, RawGmvMaxProductCreativeReportEntity>();

            try
            {
                // ✅ Collect unique identifiers từ TẤT CẢ mapped entities
                var campaignIds = allMappedEntities.Select(x => x.CampaignId).Distinct().ToList();
                var advertiserIds = allMappedEntities.Select(x => x.AdvertiserId).Distinct().ToList();
                var itemGroupIds = allMappedEntities.Select(x => x.ItemGroupId).Distinct().ToList();

                _logger.LogDebug("Batch loading existing entities for {CampaignCount} campaigns, {AdvertiserCount} advertisers, {ItemGroupCount} item groups",
                    campaignIds.Count, advertiserIds.Count, itemGroupIds.Count);

                // ✅ SINGLE batch query thay vì 10K queries! - Using optimized Dapper
                var existingEntities = await _gmvMaxProductCreativeReportDapperRepository.GetLatestByCampaignIdsAndAdvertiserIdsAndItemGroupIdsAsync(
                    campaignIds: campaignIds,
                    advertiserIds: advertiserIds,
                    itemGroupIds: itemGroupIds);

                // ✅ Tạo dictionary lookup để O(1) access
                var existingLookup = existingEntities.ToDictionary(x =>
                    $"{x.BcId}|{x.AdvertiserId}|{x.CampaignId}|{x.ItemGroupId}|{x.ItemId}|{x.StoreId}|{x.Date.Date:yyyy-MM-dd}", x => x);

                _logger.LogDebug("Loaded {ExistingCount} existing entities into lookup dictionary", existingEntities.Count);
                return existingLookup;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error batch loading existing entities for BC: {BcId}", bcId);
                return new Dictionary<string, RawGmvMaxProductCreativeReportEntity>();
            }
        }

        /// <summary>
        /// Lấy các entity hiện có trong database (Legacy method for backward compatibility)
        /// </summary>
        private async Task<List<RawGmvMaxProductCreativeReportEntity>> GetExistingEntitiesAsync(string bcId, List<RawGmvMaxProductCreativeReportEntity> mappedEntities)
        {
            if (!mappedEntities.Any())
                return new List<RawGmvMaxProductCreativeReportEntity>();

            var campaignIds = mappedEntities.Select(x => x.CampaignId).Distinct().ToList();
            var advertiserIds = mappedEntities.Select(x => x.AdvertiserId).Distinct().ToList();
            var itemGroupIds = mappedEntities.Select(x => x.ItemGroupId).Distinct().ToList();
            var query = await _gmvMaxProductCreativeReportRepository.GetLatestByCampaignIdsAndAdvertiserIdsAndItemGroupIdsAsync(
                campaignIds: campaignIds,
                advertiserIds: advertiserIds,
                itemGroupIds: itemGroupIds);

            // Lấy ra bản ghi mới nhất dựa vào trường Date
            return query;
        }

        private async Task<List<RawGmvMaxProductCreativeReportEntity>> MapListReportDataToEntitiesAsync(string bcId, string bcTimezone, List<GmvMaxProductCreativeReportData> reportDataList, DateTime syncDate)
        {
            var entities = new List<RawGmvMaxProductCreativeReportEntity>();

            foreach (var reportData in reportDataList)
            {
                var entity = MapReportDataToEntity(bcId, bcTimezone, reportData, syncDate);
                if (entity != null)
                {
                    entities.Add(entity);
                }
            }
            return entities;
        }

        /// <summary>
        /// Map dữ liệu báo cáo từ API sang entity
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="bcTimezone">Timezone của Business Center</param>
        /// <param name="reportData">Dữ liệu báo cáo</param>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="storeId">ID của Store</param>
        /// <param name="campaignId">ID của Campaign</param>
        /// <param name="itemGroupId">ID của Item Group</param>
        /// <param name="syncDate">Ngày đồng bộ</param>
        /// <returns>RawGmvMaxProductCreativeReportEntity</returns>
        private RawGmvMaxProductCreativeReportEntity? MapReportDataToEntity(string bcId, string bcTimezone, GmvMaxProductCreativeReportData productReportData, DateTime syncDate)
        {
            var reportData = productReportData.ReportItem;
            string advertiserId = productReportData.AdvertiserId;
            string storeId = productReportData.StoreId;
            string campaignId = productReportData.CampaignId;
            string itemGroupId = productReportData.ItemGroupId;

            var itemId = reportData.Dimensions?.GetValueOrDefault("item_id")?.ToString();
            if (string.IsNullOrEmpty(itemId))
            {
                _logger.LogWarning("Item ID không hợp lệ cho BC: {BcId}", bcId);
                return null;
            }

            // Convert syncDate từ timezone của BC sang UTC
            var syncDateUtc = _dateTimeService.ConvertToUtc(syncDate, bcTimezone);

            // ✅ Performance Fix: Sử dụng Guid.CreateVersion7() nếu có, hoặc tối ưu Guid generation
            var entity = new RawGmvMaxProductCreativeReportEntity(Guid.NewGuid())
            {
                BcId = bcId,
                AdvertiserId = advertiserId,
                StoreId = storeId,
                CampaignId = campaignId,
                ItemGroupId = itemGroupId,
                ItemId = itemId,
                Date = syncDateUtc,  // Lưu datetime UTC
                Currency = "USD", // Default currency
                CreativeType = productReportData.CreativeType
            };

            if (reportData.Metrics != null)
            {
                // Map các metrics từ API response
                entity.Title = GetStringValue(reportData.Metrics, "title");
                entity.TtAccountName = GetStringValue(reportData.Metrics, "tt_account_name");
                entity.TtAccountProfileImageUrl = GetStringValue(reportData.Metrics, "tt_account_profile_image_url");

                //// Map enums
                //var creativeTypeStr = GetStringValue(reportData.Metrics, "creative_type");
                //if (!string.IsNullOrEmpty(creativeTypeStr) && Enum.TryParse<TikTok.Enums.CreativeType>(creativeTypeStr, true, out var creativeType))
                //{
                //    entity.CreativeType = creativeType;
                //}

                var ttAccountAuthTypeStr = GetStringValue(reportData.Metrics, "tt_account_authorization_type");
                if (!string.IsNullOrEmpty(ttAccountAuthTypeStr) && Enum.TryParse<TikTok.Enums.TtAccountAuthorizationType>(ttAccountAuthTypeStr, true, out var ttAccountAuthType))
                {
                    entity.TtAccountAuthorizationType = ttAccountAuthType;
                }

                var shopContentTypeStr = GetStringValue(reportData.Metrics, "shop_content_type");
                if (!string.IsNullOrEmpty(shopContentTypeStr))
                {
                    switch (shopContentTypeStr.ToUpperInvariant())
                        {
                            case "VIDEO":
                                entity.ShopContentType = TikTok.Enums.ShopContentType.VIDEO;
                                break;
                            case "PRODUCT_CARD":
                                entity.ShopContentType = TikTok.Enums.ShopContentType.PRODUCT_CARD;
                                break;                           
                            default:
                                // Không set giá trị, để null
                                break;
                        }
                }

                // ✅ BUSINESS LOGIC: Nếu item_id là "-1", đặt ShopContentType là PRODUCT_CARD
                if (itemId == "-1")
                {
                    entity.ShopContentType = TikTok.Enums.ShopContentType.PRODUCT_CARD;
                }

                // ✅ BUSINESS LOGIC: Nếu item_id là "-1", đặt ShopContentType là PRODUCT_CARD
                if (itemId == "-1")
                {
                    entity.ShopContentType = TikTok.Enums.ShopContentType.PRODUCT_CARD;
                }
                var creativeDeliveryStatusStr = GetStringValue(reportData.Metrics, "creative_delivery_status");
                if (!string.IsNullOrEmpty(creativeDeliveryStatusStr) && Enum.TryParse<CreativeDeliveryStatus>(creativeDeliveryStatusStr, true, out var creativeDeliveryStatus))
                {
                    entity.CreativeDeliveryStatus = creativeDeliveryStatus;
                }
                // Map metrics
                entity.Orders = GetIntValue(reportData.Metrics, "orders");
                entity.GrossRevenue = GetDecimalValue(reportData.Metrics, "gross_revenue");
                entity.ProductImpressions = GetLongValue(reportData.Metrics, "product_impressions");
                entity.ProductClicks = GetLongValue(reportData.Metrics, "product_clicks");
                entity.ProductClickRate = GetDecimalValue(reportData.Metrics, "product_click_rate");
                entity.AdClickRate = GetDecimalValue(reportData.Metrics, "ad_click_rate");
                entity.AdConversionRate = GetDecimalValue(reportData.Metrics, "ad_conversion_rate");
                entity.AdVideoViewRate2s = GetDecimalValue(reportData.Metrics, "ad_video_view_rate_2s");
                entity.AdVideoViewRate6s = GetDecimalValue(reportData.Metrics, "ad_video_view_rate_6s");
                entity.AdVideoViewRateP25 = GetDecimalValue(reportData.Metrics, "ad_video_view_rate_p25");
                entity.AdVideoViewRateP50 = GetDecimalValue(reportData.Metrics, "ad_video_view_rate_p50");
                entity.AdVideoViewRateP75 = GetDecimalValue(reportData.Metrics, "ad_video_view_rate_p75");
                entity.AdVideoViewRateP100 = GetDecimalValue(reportData.Metrics, "ad_video_view_rate_p100");
                entity.ROI = GetDecimalValue(reportData.Metrics, "roi");
                entity.CostPerOrder = GetDecimalValue(reportData.Metrics, "cost_per_order");
                entity.Cost = GetDecimalValue(reportData.Metrics, "cost");
                var currency = GetStringValue(reportData.Metrics, "currency");
                if (!string.IsNullOrEmpty(currency))
                {
                    entity.Currency = currency;
                }
            }

            return entity;
        }

        /// <summary>
        /// Cache cho campaign names để tránh N+1 query
        /// </summary>
        private readonly Dictionary<string, string> _campaignNameCache = new Dictionary<string, string>();

        /// <summary>
        /// StringBuilder để tránh O(n²) string concatenation trong error messages
        /// </summary>
        private readonly StringBuilder _errorMessageBuilder = new StringBuilder();

        /// <summary>
        /// Safely append error message với StringBuilder để tránh O(n²) concatenation
        /// </summary>
        /// <param name="result">Sync result để append error</param>
        /// <param name="errorMessage">Error message cần append</param>
        private void AppendErrorMessage(GmvMaxProductCreativeSyncResult result, string errorMessage)
        {
            if (string.IsNullOrEmpty(errorMessage))
                return;

            // ✅ Sử dụng StringBuilder thay vì string concatenation
            _errorMessageBuilder.AppendLine(errorMessage);

            // ✅ Update result với built string (preserve existing behavior)
            result.ErrorMessage = _errorMessageBuilder.ToString();
        }

        /// <summary>
        /// Lấy tên campaign theo CampaignId với caching
        /// </summary>
        private async Task<string> GetCampaignNameAsync(string campaignId)
        {
            // ✅ Check cache first
            if (_campaignNameCache.TryGetValue(campaignId, out var cachedName))
            {
                return cachedName;
            }

            try
            {
                var campaign = await _gmvMaxCampaignsRepository.FindAsync(c => c.CampaignId == campaignId);
                var campaignName = campaign?.CampaignName ?? $"Campaign {campaignId}";

                // ✅ Cache for future use
                _campaignNameCache[campaignId] = campaignName;
                return campaignName;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting campaign name for {CampaignId}", campaignId);
                var fallbackName = $"Campaign {campaignId}";
                _campaignNameCache[campaignId] = fallbackName;
                return fallbackName;
            }
        }

        /// <summary>
        /// Xử lý batch status changes để tránh async trong loop
        /// </summary>
        /// <param name="statusChanges">Danh sách các status changes cần xử lý</param>
        private async Task ProcessStatusChangesBatchAsync(List<(RawGmvMaxProductCreativeReportEntity currentEntity, RawGmvMaxProductCreativeReportEntity mappedEntity)> statusChanges)
        {
            if (!statusChanges.Any())
                return;

            try
            {
                // ✅ Collect unique campaign IDs để batch load campaign names
                var uniqueCampaignIds = statusChanges
                    .Select(x => x.mappedEntity.CampaignId)
                    .Distinct()
                    .Where(id => !_campaignNameCache.ContainsKey(id))
                    .ToList();

                // ✅ Batch load missing campaign names (nếu có)
                if (uniqueCampaignIds.Any())
                {
                    var campaigns = await _gmvMaxCampaignsRepository.GetByCampaignIdsAsync(uniqueCampaignIds);
                    foreach (var campaign in campaigns)
                    {
                        _campaignNameCache[campaign.CampaignId] = campaign.CampaignName;
                    }
                }

                // ✅ Process status changes với cached campaign names
                foreach (var (currentEntity, mappedEntity) in statusChanges)
                {
                    // Get campaign name từ cache (đã được load ở trên)
                    var campaignName = _campaignNameCache.GetValueOrDefault(mappedEntity.CampaignId, $"Campaign {mappedEntity.CampaignId}");

                    _statusChangeTracker.TrackStatusChange(new CreativeStatusChangeInfo
                    {
                        CampaignId = mappedEntity.CampaignId,
                        CreativeId = mappedEntity.ItemId,
                        OldStatus = currentEntity.CreativeDeliveryStatus,
                        NewStatus = mappedEntity.CreativeDeliveryStatus,
                        AdvertiserId = mappedEntity.AdvertiserId,
                        BcId = mappedEntity.BcId,
                        ChangeTime = System.DateTime.UtcNow,
                        CampaignName = campaignName // ✅ No async call - từ cache!
                    });
                }

                _logger.LogDebug("Processed {Count} status changes in batch", statusChanges.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing status changes batch with {Count} items", statusChanges.Count);
                // Continue execution - không throw để không break sync
            }
        }


        /// <summary>
        /// Xử lý thông báo cho các thay đổi trạng thái creative
        /// </summary>
        private async Task ProcessStatusChangeNotificationsAsync()
        {
            try
            {
                var campaignSummaries = _statusChangeTracker.GetCampaignSummaries();

                if (!campaignSummaries.Any())
                {
                    _logger.LogDebug("No campaign summaries to process for notifications");
                    return;
                }

                _logger.LogInformation("Processing notifications for {Count} campaigns with status changes", campaignSummaries.Count);

                foreach (var summary in campaignSummaries)
                {
                    // Chỉ gửi thông báo nếu có creative có vấn đề
                    if (summary.HasProblematicStatuses)
                    {
                        _logger.LogInformation("Sending notification for campaign {CampaignId} with {ProblematicCount} problematic creatives",
                            summary.CampaignId, summary.ProblematicCreativesCount);

                        var success = await _tikTokNotificationService.SendCampaignNotificationAsync(
                            summary.CampaignId,
                            TikTokNotificationConst.GmvMaxCreativeStatusChange,
                            new Dictionary<string, object>
                            {
                                ["StatusSummary"] = summary,
                                ["ChangeTime"] = summary.LastChangeTime,
                                ["ProblematicCount"] = summary.ProblematicCreativesCount
                            });

                        if (success)
                        {
                            _logger.LogInformation("Successfully sent notification for campaign {CampaignId}", summary.CampaignId);
                        }
                        else
                        {
                            _logger.LogWarning("Failed to send notification for campaign {CampaignId}", summary.CampaignId);
                        }
                    }
                    else
                    {
                        _logger.LogDebug("Skipping notification for campaign {CampaignId} - no problematic statuses", summary.CampaignId);
                    }
                }

                // Clear tracked changes after processing
                _statusChangeTracker.Clear();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing status change notifications");
            }
        }
    }
}





