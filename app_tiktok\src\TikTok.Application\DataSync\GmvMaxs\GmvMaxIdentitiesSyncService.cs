using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Application.Contracts.MessageProviders;
using TikTok.Application.MessageProviders;
using TikTok.Consts;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Repositories;
using TikTok.TikTokApiClients;
using TikTokBusinessApi.Models;
using Volo.Abp;
using Volo.Abp.Uow;
using TikTok.DataSync.Services;
using TikTokBusinessApi;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service implementation cho việc đồng bộ dữ liệu GMV Max Identities
    /// </summary>
    public class GmvMaxIdentitiesSyncService : BaseSyncService, IGmvMaxIdentitiesSyncService
    {
        private const string SERVICE_NAME = "GmvMaxIdentitiesSyncService";

        private readonly IRawGmvMaxIdentitiesRepository _gmvMaxIdentitiesRepository;
        private readonly IAssetRepository _assetRepository;
        private readonly IBusinessCenterRepository _businessCenterRepository;
        private readonly IApiMetricsService _apiMetricsService;
        private readonly ITikTokApiClientService _tikTokApiClientService;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="serviceProvider">Service provider</param>
        /// <param name="gmvMaxIdentitiesRepository">GMV Max Identities repository</param>
        /// <param name="assetRepository">Asset repository</param>
        /// <param name="businessCenterRepository">Business Center repository</param>
        /// <param name="apiMetricsService">API metrics service</param>
        /// <param name="tikTokApiClientService">TikTok API client service</param>
        /// <param name="logger">Logger</param>
        public GmvMaxIdentitiesSyncService(
            IServiceProvider serviceProvider,
            IRawGmvMaxIdentitiesRepository gmvMaxIdentitiesRepository,
            IAssetRepository assetRepository,
            IBusinessCenterRepository businessCenterRepository,
            IApiMetricsService apiMetricsService,
            ITikTokApiClientService tikTokApiClientService,
            ILogger<GmvMaxIdentitiesSyncService> logger
            ) : base(serviceProvider, logger)
        {
            _gmvMaxIdentitiesRepository = gmvMaxIdentitiesRepository;
            _assetRepository = assetRepository;
            _businessCenterRepository = businessCenterRepository;
            _apiMetricsService = apiMetricsService;
            _tikTokApiClientService = tikTokApiClientService;
        }

        /// <summary>
        /// Đồng bộ GMV Max Identities theo Advertiser ID và BC ID
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="unauthorizedAdvertiserIds">Danh sách advertiser IDs không có quyền</param>
        /// <returns>Kết quả đồng bộ</returns>
        [UnitOfWork]
        public virtual async Task<GmvMaxIdentitiesSyncResult> SyncGmvMaxIdentitiesAsync(string advertiserId, string bcId, List<string>? unauthorizedAdvertiserIds = null)
        {
            // Bắt đầu tracking metrics nếu chưa có
            _apiMetricsService.StartTracking(SERVICE_NAME, bcId);

            var result = new GmvMaxIdentitiesSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ GMV Max Identities cho Advertiser: {AdvertiserId}", advertiserId);

                // Tạo TikTok client từ Service
                var tikTokClient = await _tikTokApiClientService.GetOrCreateClientAsync(bcId);

                // Lấy danh sách stores cho advertiser này
                var stores = await GetStoresForAdvertiser(advertiserId, bcId);

                if (!stores.Any())
                {
                    _logger.LogDebug("Không tìm thấy Store nào cho Advertiser: {AdvertiserId}", advertiserId);
                    result.EndTime = DateTime.UtcNow;
                    return result;
                }

                var totalIdentities = 0;

                // Xử lý và lưu dữ liệu từng store để giữ thông tin store
                foreach (var store in stores)
                {
                    try
                    {
                        var storeIdentities = await GetIdentitiesForStore(tikTokClient, advertiserId, store.StoreId, store.StoreAuthorizedBcId, unauthorizedAdvertiserIds);
                        if (storeIdentities.Any())
                        {
                            totalIdentities += storeIdentities.Count;
                            await ProcessAndSaveIdentities(storeIdentities, advertiserId, bcId, store.StoreId, store.StoreAuthorizedBcId, result);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi xử lý identities cho Store: {StoreId}", store.StoreId);
                        result.ErrorRecords++;
                    }
                }

                if (totalIdentities == 0)
                {
                    _logger.LogDebug("Không có Identity nào được trả về từ API cho Advertiser: {AdvertiserId}", advertiserId);
                    result.EndTime = DateTime.UtcNow;
                    return result;
                }

                _logger.LogDebug("Nhận được {Count} Identity từ API cho Advertiser: {AdvertiserId}", totalIdentities, advertiserId);

                result.AdvertiserCount = 1;
                result.EndTime = DateTime.UtcNow;
                _logger.LogDebug("Hoàn thành đồng bộ GMV Max Identities cho Advertiser: {AdvertiserId}, Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, Lỗi: {Error}",
                    advertiserId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.ErrorRecords);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                result.EndTime = DateTime.UtcNow;
                _logger.LogError(ex, "Lỗi nghiệp vụ khi đồng bộ GMV Max Identities cho Advertiser: {AdvertiserId}", advertiserId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ GMV Max Identities: {ex.Message}";
                result.EndTime = DateTime.UtcNow;
                _logger.LogError(ex, "Lỗi khi đồng bộ GMV Max Identities cho Advertiser: {AdvertiserId}", advertiserId);
            }
            finally
            {
                // Luôn lấy metrics trước khi return, kể cả khi có exception
                result.ApiMetrics = _apiMetricsService.GetMetrics(SERVICE_NAME, bcId);
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ nhiều GMV Max Identities cho nhiều Advertiser
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="advertiserIds">Danh sách ID của Advertiser (null để đồng bộ tất cả)</param>
        /// <returns>Kết quả đồng bộ</returns>
        [UnitOfWork]
        public virtual async Task<GmvMaxIdentitiesSyncResult> SyncManyGmvMaxIdentitiesAsync(string bcId, List<string>? advertiserIds = null)
        {
            // Bắt đầu tracking metrics nếu chưa có
            _apiMetricsService.StartTracking(SERVICE_NAME, bcId);

            var result = new GmvMaxIdentitiesSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                // Nếu advertiserIds là null, lấy tất cả advertiser IDs từ asset repository
                if (advertiserIds == null)
                {
                    var advertisers = await _assetRepository.GetByBcIdAsync(bcId, assetType: Enums.AssetType.ADVERTISER);
                    advertiserIds = advertisers.Select(x => x.AssetId).ToList();
                    _logger.LogDebug("Lấy danh sách Advertiser IDs từ Asset Repository cho BC: {BcId}, Số Advertiser: {Count}", bcId, advertiserIds.Count);
                }

                if (!advertiserIds.Any())
                {
                    _logger.LogWarning("Không tìm thấy Advertiser nào cho BC: {BcId}", bcId);
                    result.ErrorMessage = $"Không tìm thấy Advertiser nào cho BC: {bcId}";
                    return result;
                }

                _logger.LogDebug("Bắt đầu đồng bộ nhiều GMV Max Identities cho BC: {BcId}, Số Advertiser: {Count}", bcId, advertiserIds.Count);

                var unauthorizedAdvertiserIds = new List<string>();
                foreach (var advertiserId in advertiserIds)
                {
                    try
                    {
                        var singleResult = await SyncGmvMaxIdentitiesAsync(advertiserId, bcId, unauthorizedAdvertiserIds);
                        result.NewRecords += singleResult.NewRecords;
                        result.UpdatedRecords += singleResult.UpdatedRecords;
                        result.ErrorRecords += singleResult.ErrorRecords;
                        result.ProductGmvMaxAvailableCount += singleResult.ProductGmvMaxAvailableCount;
                        result.LiveGmvMaxAvailableCount += singleResult.LiveGmvMaxAvailableCount;
                        result.UnavailableCount += singleResult.UnavailableCount;
                        result.RunningCustomShopAdsCount += singleResult.RunningCustomShopAdsCount;

                        // Merge identity type counts
                        foreach (var kvp in singleResult.IdentityTypeCount)
                        {
                            if (result.IdentityTypeCount.ContainsKey(kvp.Key))
                                result.IdentityTypeCount[kvp.Key] += kvp.Value;
                            else
                                result.IdentityTypeCount[kvp.Key] = kvp.Value;
                        }

                        // Merge unavailable reason counts
                        foreach (var kvp in singleResult.UnavailableReasonCount)
                        {
                            if (result.UnavailableReasonCount.ContainsKey(kvp.Key))
                                result.UnavailableReasonCount[kvp.Key] += kvp.Value;
                            else
                                result.UnavailableReasonCount[kvp.Key] = kvp.Value;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ GMV Max Identities cho Advertiser: {AdvertiserId}", advertiserId);
                        result.ErrorRecords++;
                    }
                }

                result.AdvertiserCount = advertiserIds.Count;
                result.UnauthorizedAdvertiserIds = unauthorizedAdvertiserIds;
                _logger.LogDebug("Hoàn thành đồng bộ nhiều GMV Max Identities cho BC: {BcId}, Tổng: {Total}", bcId, result.TotalSynced);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi nghiệp vụ khi đồng bộ nhiều GMV Max Identities cho BC: {BcId}", bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ nhiều GMV Max Identities: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ nhiều GMV Max Identities cho BC: {BcId}", bcId);
            }
            finally
            {
                // Luôn lấy metrics trước khi return, kể cả khi có exception
                result.ApiMetrics = _apiMetricsService.GetMetrics(SERVICE_NAME, bcId);
            }

            result.EndTime = DateTime.UtcNow;
            return result;
        }

        /// <summary>
        /// Đồng bộ tất cả GMV Max Identities cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        [UnitOfWork]
        public virtual async Task<GmvMaxIdentitiesSyncResult> SyncAllGmvMaxIdentitiesAsync()
        {
            // Bắt đầu tracking metrics
            _apiMetricsService.StartTracking(SERVICE_NAME);

            var result = new GmvMaxIdentitiesSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ tất cả GMV Max Identities cho tất cả BC");

                // Lấy tất cả Business Centers
                var businessCenters = await _businessCenterRepository.GetListAsync();

                if (!businessCenters.Any())
                {
                    _logger.LogWarning("Không tìm thấy Business Center nào");
                    result.ErrorMessage = "Không tìm thấy Business Center nào";
                    return result;
                }

                foreach (var bc in businessCenters)
                {
                    try
                    {
                        var bcResult = await SyncManyGmvMaxIdentitiesAsync(bc.BcId);
                        result.NewRecords += bcResult.NewRecords;
                        result.UpdatedRecords += bcResult.UpdatedRecords;
                        result.ErrorRecords += bcResult.ErrorRecords;
                        result.AdvertiserCount += bcResult.AdvertiserCount;
                        result.ProductGmvMaxAvailableCount += bcResult.ProductGmvMaxAvailableCount;
                        result.LiveGmvMaxAvailableCount += bcResult.LiveGmvMaxAvailableCount;
                        result.UnavailableCount += bcResult.UnavailableCount;
                        result.RunningCustomShopAdsCount += bcResult.RunningCustomShopAdsCount;

                        // Merge counts
                        foreach (var kvp in bcResult.IdentityTypeCount)
                        {
                            if (result.IdentityTypeCount.ContainsKey(kvp.Key))
                                result.IdentityTypeCount[kvp.Key] += kvp.Value;
                            else
                                result.IdentityTypeCount[kvp.Key] = kvp.Value;
                        }

                        foreach (var kvp in bcResult.UnavailableReasonCount)
                        {
                            if (result.UnavailableReasonCount.ContainsKey(kvp.Key))
                                result.UnavailableReasonCount[kvp.Key] += kvp.Value;
                            else
                                result.UnavailableReasonCount[kvp.Key] = kvp.Value;
                        }

                        result.UnauthorizedAdvertiserIds.AddRange(bcResult.UnauthorizedAdvertiserIds);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ GMV Max Identities cho BC: {BcId}", bc.BcId);
                        result.ErrorRecords++;
                    }
                }

                result.BcCount = businessCenters.Count;
                _logger.LogDebug("Hoàn thành đồng bộ tất cả GMV Max Identities cho tất cả BC, Tổng: {Total}", result.TotalSynced);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ tất cả GMV Max Identities: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ tất cả GMV Max Identities cho tất cả BC");
            }
            finally
            {
                // Luôn lấy metrics trước khi return, kể cả khi có exception
                result.ApiMetrics = _apiMetricsService.GetMetrics(SERVICE_NAME);
            }

            result.EndTime = DateTime.UtcNow;
            return result;
        }

        /// <summary>
        /// Xử lý và lưu danh sách identities với rollback support
        /// </summary>
        /// <param name="identityList">Danh sách identity từ API</param>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="storeId">ID của Store</param>
        /// <param name="storeAuthorizedBcId">ID của BC được ủy quyền truy cập store</param>
        /// <param name="result">Kết quả đồng bộ</param>
        private async Task ProcessAndSaveIdentities(List<GMVMaxIdentity> identityList, string advertiserId, string bcId, string? storeId, string? storeAuthorizedBcId, GmvMaxIdentitiesSyncResult result)
        {
            using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true);

            var insertedEntities = new List<RawGmvMaxIdentitiesEntity>();
            var updatedEntities = new List<RawGmvMaxIdentitiesEntity>();
            var originalEntities = new List<RawGmvMaxIdentitiesEntity>(); // Để rollback

            try
            {
                // Convert dữ liệu từ API thành danh sách entity
                var mappedEntities = MapListApiDataToEntities(identityList, advertiserId, bcId, storeId, storeAuthorizedBcId);

                // Lấy danh sách đã có trong DB sử dụng composite key  
                var existingEntities = await GetExistingEntitiesByCompositeKeys(mappedEntities);
                var existingDict = existingEntities.ToDictionary(x => $"{x.AdvertiserId}|{x.StoreId}|{x.StoreAuthorizedBcId}|{x.IdentityId}", x => x);

                // Duyệt từng bản ghi xử lý thêm mới/cập nhật
                foreach (var mappedEntity in mappedEntities)
                {
                    try
                    {
                        var compositeKey = $"{mappedEntity.AdvertiserId}|{mappedEntity.StoreId}|{mappedEntity.StoreAuthorizedBcId}|{mappedEntity.IdentityId}";

                        if (!existingDict.TryGetValue(compositeKey, out var existingEntity))
                        {
                            // Thêm mới
                            mappedEntity.SyncedAt = DateTime.UtcNow;
                            insertedEntities.Add(mappedEntity);
                            result.NewRecords++;
                        }
                        else
                        {
                            // Lưu trạng thái ban đầu để rollback
                            var originalEntity = new RawGmvMaxIdentitiesEntity(existingEntity.Id);
                            originalEntity.UpdateFromNewData(existingEntity);
                            originalEntities.Add(originalEntity);

                            // Cập nhật nếu có thay đổi
                            if (existingEntity.HasChanged(mappedEntity))
                            {
                                UpdateEntityFromNewData(existingEntity, mappedEntity);
                                existingEntity.SyncedAt = DateTime.UtcNow;
                                updatedEntities.Add(existingEntity);
                                result.UpdatedRecords++;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi xử lý GMV Max Identity: {IdentityId}", mappedEntity.IdentityId);
                        result.ErrorRecords++;
                    }
                }

                // Batch save với rollback support
                await SaveEntitiesWithRollback(insertedEntities, updatedEntities, originalEntities);

                await uow.CompleteAsync();
                _logger.LogDebug("Hoàn thành xử lý và lưu {NewCount} mới, {UpdateCount} cập nhật GMV Max Identity",
                    insertedEntities.Count, updatedEntities.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi xử lý và lưu GMV Max Identities, thực hiện rollback");

                // Rollback các thay đổi
                await RollbackChanges(insertedEntities, updatedEntities, originalEntities);

                // Re-throw exception
                throw;
            }
        }

        /// <summary>
        /// Lấy danh sách entity đã tồn tại theo composite keys
        /// </summary>
        /// <param name="mappedEntities">Danh sách entities để tìm kiếm</param>
        /// <returns>Danh sách entity đã tồn tại</returns>
        private async Task<List<RawGmvMaxIdentitiesEntity>> GetExistingEntitiesByCompositeKeys(List<RawGmvMaxIdentitiesEntity> mappedEntities)
        {
            var existingEntities = new List<RawGmvMaxIdentitiesEntity>();

            foreach (var entity in mappedEntities)
            {
                var existing = await _gmvMaxIdentitiesRepository.GetByCompositeKeyAsync(
                    entity.AdvertiserId,
                    entity.StoreId,
                    entity.StoreAuthorizedBcId,
                    entity.IdentityId);

                if (existing != null)
                {
                    existingEntities.Add(existing);
                }
            }

            return existingEntities;
        }

        /// <summary>
        /// Convert danh sách dữ liệu API thành danh sách entity
        /// </summary>
        /// <param name="identityList">Danh sách dữ liệu từ API</param>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="storeId">ID của Store</param>
        /// <param name="storeAuthorizedBcId">ID của BC được ủy quyền truy cập store</param>
        /// <returns>Danh sách entity</returns>
        private List<RawGmvMaxIdentitiesEntity> MapListApiDataToEntities(List<GMVMaxIdentity> identityList, string advertiserId, string bcId, string? storeId, string? storeAuthorizedBcId)
        {
            var entities = new List<RawGmvMaxIdentitiesEntity>();

            foreach (var identity in identityList)
            {
                try
                {
                    var entity = MapApiDataToEntity(identity, advertiserId, bcId, storeId, storeAuthorizedBcId);
                    entities.Add(entity);
                }
                catch (Exception ex)
                {
                    var identityId = identity?.IdentityId ?? "unknown";
                    _logger.LogError(ex, "Lỗi khi map dữ liệu API sang Entity cho Identity: {IdentityId}", identityId);
                }
            }

            return entities;
        }

        /// <summary>
        /// Map dữ liệu từ API sang Entity
        /// </summary>
        /// <param name="identity">Dữ liệu identity từ API</param>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="storeId">ID của Store</param>
        /// <param name="storeAuthorizedBcId">ID của BC được ủy quyền truy cập store</param>
        /// <returns>Entity</returns>
        private RawGmvMaxIdentitiesEntity MapApiDataToEntity(GMVMaxIdentity identity, string advertiserId, string bcId, string? storeId, string? storeAuthorizedBcId)
        {
            var entity = new RawGmvMaxIdentitiesEntity(Guid.NewGuid())
            {
                BcId = bcId,
                AdvertiserId = advertiserId,
                IdentityId = identity.IdentityId ?? string.Empty,
                IdentityType = identity.IdentityType ?? string.Empty,
                DisplayName = identity.DisplayName,
                UserName = identity.UserName,
                ProfileImage = identity.ProfileImage,
                IdentityAuthorizedBcId = identity.IdentityAuthorizedBcId,
                StoreId = storeId ?? identity.StoreId,
                StoreAuthorizedBcId = storeAuthorizedBcId,
                LiveGmvMaxAvailable = identity.LiveGMVMaxAvailable ?? false,
                ProductGmvMaxAvailable = identity.ProductGMVMaxAvailable ?? false,
                IsRunningCustomShopAds = identity.IsRunningCustomShopAds ?? false,
                UnavailableReason = identity.UnavailableReason,
                SyncedAt = DateTime.UtcNow
            };

            return entity;
        }


        /// <summary>
        /// Cập nhật entity từ dữ liệu mới
        /// </summary>
        /// <param name="existingEntity">Entity hiện tại</param>
        /// <param name="newEntity">Entity mới</param>
        private void UpdateEntityFromNewData(RawGmvMaxIdentitiesEntity existingEntity, RawGmvMaxIdentitiesEntity newEntity)
        {
            existingEntity.UpdateFromNewData(newEntity);
        }


        /// <summary>
        /// Lấy danh sách stores cho advertiser
        /// </summary>
        /// <param name="advertiserId">ID của advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Danh sách store info</returns>
        private async Task<List<(string StoreId, string StoreAuthorizedBcId)>> GetStoresForAdvertiser(string advertiserId, string bcId)
        {
            // Lấy danh sách stores từ asset repository
            // Tạm thời sử dụng logic đơn giản, có thể cần cải tiến sau
            var stores = await _assetRepository.GetByBcIdAsync(bcId, assetType: Enums.AssetType.TIKTOK_SHOP);

            return stores.Select(store => (store.AssetId, bcId)).ToList();
        }

        /// <summary>
        /// Lấy danh sách identities cho một store cụ thể
        /// </summary>
        /// <param name="tikTokClient">TikTok API client</param>
        /// <param name="advertiserId">ID của advertiser</param>
        /// <param name="storeId">ID của store</param>
        /// <param name="storeAuthorizedBcId">ID của BC được ủy quyền truy cập store</param>
        /// <param name="unauthorizedAdvertiserIds">Danh sách advertiser IDs không có quyền</param>
        /// <returns>Danh sách identity data từ API</returns>
        private async Task<List<GMVMaxIdentity>> GetIdentitiesForStore(
            TikTokBusinessApiClient tikTokClient,
            string advertiserId,
            string storeId,
            string storeAuthorizedBcId,
            List<string>? unauthorizedAdvertiserIds)
        {
            try
            {
                _logger.LogDebug("Gọi API lấy identities cho Store: {StoreId}, Advertiser: {AdvertiserId}", storeId, advertiserId);

                // Gọi API GetIdentitiesAsync với đúng tham số
                var response = await tikTokClient.GMVMax.GetIdentitiesAsync(advertiserId, storeId, storeAuthorizedBcId);

                if (response == null)
                {
                    throw new BusinessException("0", "No response from API");
                }

                if (!TikTokApiCodes.IsSuccess(response.Code))
                {
                    // Xử lý lỗi quyền truy cập
                    if (AdAccountSyncHelper.IsPermissionError(response) && unauthorizedAdvertiserIds != null)
                    {
                        var responseMessage = response.Message ?? string.Empty;
                        var batchUnauthorizedIds = AdAccountSyncHelper.ParseUnauthorizedAdvertiserIds(responseMessage);
                        if (batchUnauthorizedIds?.Count > 0)
                        {
                            unauthorizedAdvertiserIds!.AddRange(batchUnauthorizedIds);
                        }
                    }
                    throw new BusinessException(response.Code.ToString(), response.Message ?? "Unknown error");
                }

                if (response.Data?.IdentityList == null || response.Data.IdentityList.Count == 0)
                {
                    _logger.LogDebug("Không có Identity nào từ API cho Store: {StoreId}", storeId);
                    return new List<GMVMaxIdentity>();
                }

                var identities = response.Data.IdentityList;

                var logMessage = $"Nhận được {identities.Count} Identity từ API cho Store: {storeId}";
                _logger.LogInformation(logMessage);

                return identities;
            }
            catch (BusinessException)
            {
                throw; // Re-throw business exceptions
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi gọi API lấy identities cho Store: {StoreId}", storeId);
                throw;
            }
        }



        /// <summary>
        /// Lưu entities với rollback support
        /// </summary>
        /// <param name="insertedEntities">Entities cần thêm mới</param>
        /// <param name="updatedEntities">Entities cần cập nhật</param>
        /// <param name="originalEntities">Entities gốc để rollback</param>
        private async Task SaveEntitiesWithRollback(
            List<RawGmvMaxIdentitiesEntity> insertedEntities,
            List<RawGmvMaxIdentitiesEntity> updatedEntities,
            List<RawGmvMaxIdentitiesEntity> originalEntities)
        {
            try
            {
                // Thêm các bản ghi mới
                if (insertedEntities.Any())
                {
                    await _gmvMaxIdentitiesRepository.InsertManyAsync(insertedEntities, autoSave: false);
                    _logger.LogDebug("Thêm mới {Count} GMV Max Identity", insertedEntities.Count);
                }

                // Cập nhật các bản ghi đã tồn tại
                if (updatedEntities.Any())
                {
                    await _gmvMaxIdentitiesRepository.UpdateManyAsync(updatedEntities, autoSave: false);
                    _logger.LogDebug("Cập nhật {Count} GMV Max Identity", updatedEntities.Count);
                }

                // Save changes will be handled by UnitOfWork
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi lưu GMV Max Identities, thực hiện rollback");
                throw;
            }
        }

        /// <summary>
        /// Rollback các thay đổi khi có lỗi
        /// </summary>
        /// <param name="insertedEntities">Entities đã thêm mới cần xóa</param>
        /// <param name="updatedEntities">Entities đã cập nhật cần khôi phục</param>
        /// <param name="originalEntities">Entities gốc để khôi phục</param>
        private async Task RollbackChanges(
            List<RawGmvMaxIdentitiesEntity> insertedEntities,
            List<RawGmvMaxIdentitiesEntity> updatedEntities,
            List<RawGmvMaxIdentitiesEntity> originalEntities)
        {
            try
            {
                _logger.LogWarning("Bắt đầu rollback {InsertCount} inserted và {UpdateCount} updated GMV Max Identities",
                    insertedEntities.Count, updatedEntities.Count);

                // Xóa các bản ghi đã thêm mới
                if (insertedEntities.Any())
                {
                    var insertedIds = insertedEntities.Select(x => x.Id).ToList();
                    var entitiesToDelete = await _gmvMaxIdentitiesRepository.GetListAsync(x => insertedIds.Contains(x.Id));
                    if (entitiesToDelete.Any())
                    {
                        await _gmvMaxIdentitiesRepository.DeleteManyAsync(entitiesToDelete, autoSave: false);
                        _logger.LogDebug("Rollback: Xóa {Count} GMV Max Identity đã thêm mới", entitiesToDelete.Count);
                    }
                }

                // Khôi phục các bản ghi đã cập nhật về trạng thái ban đầu
                if (originalEntities.Any())
                {
                    var originalDict = originalEntities.ToDictionary(x => x.Id, x => x);
                    foreach (var updatedEntity in updatedEntities)
                    {
                        if (originalDict.TryGetValue(updatedEntity.Id, out var originalEntity))
                        {
                            UpdateEntityFromNewData(updatedEntity, originalEntity);
                        }
                    }

                    await _gmvMaxIdentitiesRepository.UpdateManyAsync(updatedEntities, autoSave: false);
                    _logger.LogDebug("Rollback: Khôi phục {Count} GMV Max Identity về trạng thái ban đầu", updatedEntities.Count);
                }

                // Save rollback changes will be handled by UnitOfWork
                _logger.LogInformation("Hoàn thành rollback GMV Max Identities");
            }
            catch (Exception rollbackEx)
            {
                _logger.LogError(rollbackEx, "Lỗi nghiêm trọng: Không thể rollback GMV Max Identities");
                // Không throw exception ở đây để tránh che giấu lỗi gốc
            }
        }
    }
}
