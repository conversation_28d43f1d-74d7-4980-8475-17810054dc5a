using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Application.Contracts.MessageProviders;
using TikTok.Application.MessageProviders;
using TikTok.Consts;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Repositories;
using TikTok.TikTokApiClients;
using TikTokBusinessApi.Models;
using Volo.Abp;
using Volo.Abp.Uow;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service implementation cho việc đồng bộ dữ liệu GMV Max Identities
    /// </summary>
    public class GmvMaxIdentitiesSyncService : BaseSyncService, IGmvMaxIdentitiesSyncService
    {
        private readonly IRawGmvMaxIdentitiesRepository _gmvMaxIdentitiesRepository;
        private readonly IAssetRepository _assetRepository;
        private readonly IBusinessCenterRepository _businessCenterRepository;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="serviceProvider">Service provider</param>
        /// <param name="gmvMaxIdentitiesRepository">GMV Max Identities repository</param>
        /// <param name="assetRepository">Asset repository</param>
        /// <param name="businessCenterRepository">Business Center repository</param>
        /// <param name="logger">Logger</param>
        public GmvMaxIdentitiesSyncService(
            IServiceProvider serviceProvider,
            IRawGmvMaxIdentitiesRepository gmvMaxIdentitiesRepository,
            IAssetRepository assetRepository,
            IBusinessCenterRepository businessCenterRepository,
            ILogger<GmvMaxIdentitiesSyncService> logger
            ) : base(serviceProvider, logger)
        {
            _gmvMaxIdentitiesRepository = gmvMaxIdentitiesRepository;
            _assetRepository = assetRepository;
            _businessCenterRepository = businessCenterRepository;
        }

        /// <summary>
        /// Đồng bộ GMV Max Identities theo Advertiser ID và BC ID
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="unauthorizedAdvertiserIds">Danh sách advertiser IDs không có quyền</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxIdentitiesSyncResult> SyncGmvMaxIdentitiesAsync(string advertiserId, string bcId, List<string>? unauthorizedAdvertiserIds = null)
        {
            var result = new GmvMaxIdentitiesSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ GMV Max Identities cho Advertiser: {AdvertiserId}", advertiserId);

                // Tạo TikTok client từ Singleton
                using var tikTokClient = await CreateTikTokBusinessApiClient(bcId);

                // Gọi API lấy danh sách identities
                // Note: Sử dụng API endpoint thực tế để lấy identities
                // Có thể cần sử dụng một API endpoint khác hoặc thêm method mới vào TikTok Business API client

                // Tạm thời sử dụng mock data để demo, cần thay thế bằng API call thực tế
                var mockIdentityList = CreateMockIdentityList();

                if (mockIdentityList == null || !mockIdentityList.Any())
                {
                    _logger.LogDebug("Không có Identity nào được trả về từ API cho Advertiser: {AdvertiserId}", advertiserId);
                    result.EndTime = DateTime.UtcNow;
                    return result;
                }

                _logger.LogDebug("Nhận được {Count} Identity từ API cho Advertiser: {AdvertiserId}", mockIdentityList.Count, advertiserId);

                // Xử lý và lưu dữ liệu
                await ProcessAndSaveIdentities(mockIdentityList, advertiserId, bcId, result);

                result.AdvertiserCount = 1;
                result.EndTime = DateTime.UtcNow;
                _logger.LogDebug("Hoàn thành đồng bộ GMV Max Identities cho Advertiser: {AdvertiserId}, Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, Lỗi: {Error}",
                    advertiserId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.ErrorRecords);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                result.EndTime = DateTime.UtcNow;
                _logger.LogError(ex, "Lỗi nghiệp vụ khi đồng bộ GMV Max Identities cho Advertiser: {AdvertiserId}", advertiserId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ GMV Max Identities: {ex.Message}";
                result.EndTime = DateTime.UtcNow;
                _logger.LogError(ex, "Lỗi khi đồng bộ GMV Max Identities cho Advertiser: {AdvertiserId}", advertiserId);
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ nhiều GMV Max Identities cho nhiều Advertiser
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="advertiserIds">Danh sách ID của Advertiser (null để đồng bộ tất cả)</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxIdentitiesSyncResult> SyncManyGmvMaxIdentitiesAsync(string bcId, List<string>? advertiserIds = null)
        {
            var result = new GmvMaxIdentitiesSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                // Nếu advertiserIds là null, lấy tất cả advertiser IDs từ asset repository
                if (advertiserIds == null)
                {
                    var advertisers = await _assetRepository.GetByBcIdAsync(bcId, assetType: Enums.AssetType.ADVERTISER);
                    advertiserIds = advertisers.Select(x => x.AssetId).ToList();
                    _logger.LogDebug("Lấy danh sách Advertiser IDs từ Asset Repository cho BC: {BcId}, Số Advertiser: {Count}", bcId, advertiserIds.Count);
                }

                if (!advertiserIds.Any())
                {
                    _logger.LogWarning("Không tìm thấy Advertiser nào cho BC: {BcId}", bcId);
                    result.ErrorMessage = $"Không tìm thấy Advertiser nào cho BC: {bcId}";
                    return result;
                }

                _logger.LogDebug("Bắt đầu đồng bộ nhiều GMV Max Identities cho BC: {BcId}, Số Advertiser: {Count}", bcId, advertiserIds.Count);

                var unauthorizedAdvertiserIds = new List<string>();
                foreach (var advertiserId in advertiserIds)
                {
                    try
                    {
                        var singleResult = await SyncGmvMaxIdentitiesAsync(advertiserId, bcId, unauthorizedAdvertiserIds);
                        result.NewRecords += singleResult.NewRecords;
                        result.UpdatedRecords += singleResult.UpdatedRecords;
                        result.ErrorRecords += singleResult.ErrorRecords;
                        result.ProductGmvMaxAvailableCount += singleResult.ProductGmvMaxAvailableCount;
                        result.LiveGmvMaxAvailableCount += singleResult.LiveGmvMaxAvailableCount;
                        result.UnavailableCount += singleResult.UnavailableCount;
                        result.RunningCustomShopAdsCount += singleResult.RunningCustomShopAdsCount;

                        // Merge identity type counts
                        foreach (var kvp in singleResult.IdentityTypeCount)
                        {
                            if (result.IdentityTypeCount.ContainsKey(kvp.Key))
                                result.IdentityTypeCount[kvp.Key] += kvp.Value;
                            else
                                result.IdentityTypeCount[kvp.Key] = kvp.Value;
                        }

                        // Merge unavailable reason counts
                        foreach (var kvp in singleResult.UnavailableReasonCount)
                        {
                            if (result.UnavailableReasonCount.ContainsKey(kvp.Key))
                                result.UnavailableReasonCount[kvp.Key] += kvp.Value;
                            else
                                result.UnavailableReasonCount[kvp.Key] = kvp.Value;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ GMV Max Identities cho Advertiser: {AdvertiserId}", advertiserId);
                        result.ErrorRecords++;
                    }
                }

                result.AdvertiserCount = advertiserIds.Count;
                result.UnauthorizedAdvertiserIds = unauthorizedAdvertiserIds;
                _logger.LogDebug("Hoàn thành đồng bộ nhiều GMV Max Identities cho BC: {BcId}, Tổng: {Total}", bcId, result.TotalSynced);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi nghiệp vụ khi đồng bộ nhiều GMV Max Identities cho BC: {BcId}", bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ nhiều GMV Max Identities: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ nhiều GMV Max Identities cho BC: {BcId}", bcId);
            }

            result.EndTime = DateTime.UtcNow;
            return result;
        }

        /// <summary>
        /// Đồng bộ tất cả GMV Max Identities cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxIdentitiesSyncResult> SyncAllGmvMaxIdentitiesAsync()
        {
            var result = new GmvMaxIdentitiesSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ tất cả GMV Max Identities cho tất cả BC");

                // Lấy tất cả Business Centers
                var businessCenters = await _businessCenterRepository.GetListAsync();

                if (!businessCenters.Any())
                {
                    _logger.LogWarning("Không tìm thấy Business Center nào");
                    result.ErrorMessage = "Không tìm thấy Business Center nào";
                    return result;
                }

                foreach (var bc in businessCenters)
                {
                    try
                    {
                        var bcResult = await SyncManyGmvMaxIdentitiesAsync(bc.BcId);
                        result.NewRecords += bcResult.NewRecords;
                        result.UpdatedRecords += bcResult.UpdatedRecords;
                        result.ErrorRecords += bcResult.ErrorRecords;
                        result.AdvertiserCount += bcResult.AdvertiserCount;
                        result.ProductGmvMaxAvailableCount += bcResult.ProductGmvMaxAvailableCount;
                        result.LiveGmvMaxAvailableCount += bcResult.LiveGmvMaxAvailableCount;
                        result.UnavailableCount += bcResult.UnavailableCount;
                        result.RunningCustomShopAdsCount += bcResult.RunningCustomShopAdsCount;

                        // Merge counts
                        foreach (var kvp in bcResult.IdentityTypeCount)
                        {
                            if (result.IdentityTypeCount.ContainsKey(kvp.Key))
                                result.IdentityTypeCount[kvp.Key] += kvp.Value;
                            else
                                result.IdentityTypeCount[kvp.Key] = kvp.Value;
                        }

                        foreach (var kvp in bcResult.UnavailableReasonCount)
                        {
                            if (result.UnavailableReasonCount.ContainsKey(kvp.Key))
                                result.UnavailableReasonCount[kvp.Key] += kvp.Value;
                            else
                                result.UnavailableReasonCount[kvp.Key] = kvp.Value;
                        }

                        result.UnauthorizedAdvertiserIds.AddRange(bcResult.UnauthorizedAdvertiserIds);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ GMV Max Identities cho BC: {BcId}", bc.BcId);
                        result.ErrorRecords++;
                    }
                }

                result.BcCount = businessCenters.Count;
                _logger.LogDebug("Hoàn thành đồng bộ tất cả GMV Max Identities cho tất cả BC, Tổng: {Total}", result.TotalSynced);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ tất cả GMV Max Identities: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ tất cả GMV Max Identities cho tất cả BC");
            }

            result.EndTime = DateTime.UtcNow;
            return result;
        }

        /// <summary>
        /// Xử lý và lưu danh sách identities
        /// </summary>
        /// <param name="identityList">Danh sách identity từ API</param>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="result">Kết quả đồng bộ</param>
        private async Task ProcessAndSaveIdentities(List<dynamic> identityList, string advertiserId, string bcId, GmvMaxIdentitiesSyncResult result)
        {
            using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false);

            // Convert dữ liệu từ API thành danh sách entity
            var mappedEntities = MapListApiDataToEntities(identityList, advertiserId, bcId);

            // Lấy danh sách identity IDs để query DB
            var identityIds = mappedEntities.Select(x => x.IdentityId).ToList();

            // Lấy danh sách đã có trong DB
            var existingEntities = await GetExistingEntitiesByAdvertiserAndIdentityIds(advertiserId, identityIds);
            var existingDict = existingEntities.ToDictionary(x => x.IdentityId, x => x);

            var insertedEntities = new List<RawGmvMaxIdentitiesEntity>();
            var updatedEntities = new List<RawGmvMaxIdentitiesEntity>();

            // Duyệt từng bản ghi xử lý thêm mới/cập nhật
            foreach (var mappedEntity in mappedEntities)
            {
                try
                {
                    // Update statistics
                    UpdateResultStatistics(mappedEntity, result);

                    if (!existingDict.TryGetValue(mappedEntity.IdentityId, out var existingEntity))
                    {
                        // Thêm mới
                        mappedEntity.SyncedAt = DateTime.UtcNow;
                        insertedEntities.Add(mappedEntity);
                        result.NewRecords++;
                    }
                    else
                    {
                        // Cập nhật nếu có thay đổi
                        if (existingEntity.HasChanged(mappedEntity))
                        {
                            UpdateEntityFromNewData(existingEntity, mappedEntity);
                            existingEntity.SyncedAt = DateTime.UtcNow;
                            updatedEntities.Add(existingEntity);
                            result.UpdatedRecords++;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Lỗi khi xử lý GMV Max Identity: {IdentityId}", mappedEntity.IdentityId);
                    result.ErrorRecords++;
                }
            }

            // Thêm các bản ghi mới vào kho dữ liệu
            if (insertedEntities.Any())
            {
                await _gmvMaxIdentitiesRepository.InsertManyAsync(insertedEntities, autoSave: true);
                _logger.LogDebug("Thêm mới {Count} GMV Max Identity", insertedEntities.Count);
            }

            // Cập nhật các bản ghi đã tồn tại
            if (updatedEntities.Any())
            {
                await _gmvMaxIdentitiesRepository.UpdateManyAsync(updatedEntities, autoSave: true);
                _logger.LogDebug("Cập nhật {Count} GMV Max Identity", updatedEntities.Count);
            }

            await uow.CompleteAsync();
        }

        /// <summary>
        /// Lấy danh sách entity đã tồn tại theo advertiser và identity IDs
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="identityIds">Danh sách identity IDs</param>
        /// <returns>Danh sách entity đã tồn tại</returns>
        private async Task<List<RawGmvMaxIdentitiesEntity>> GetExistingEntitiesByAdvertiserAndIdentityIds(string advertiserId, List<string> identityIds)
        {
            var allEntities = await _gmvMaxIdentitiesRepository.GetByAdvertiserIdAsync(advertiserId);
            return allEntities.Where(x => identityIds.Contains(x.IdentityId)).ToList();
        }

        /// <summary>
        /// Convert danh sách dữ liệu API thành danh sách entity
        /// </summary>
        /// <param name="identityList">Danh sách dữ liệu từ API</param>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Danh sách entity</returns>
        private List<RawGmvMaxIdentitiesEntity> MapListApiDataToEntities(List<dynamic> identityList, string advertiserId, string bcId)
        {
            var entities = new List<RawGmvMaxIdentitiesEntity>();

            foreach (var identity in identityList)
            {
                try
                {
                    var entity = MapApiDataToEntity(identity, advertiserId, bcId);
                    entities.Add(entity);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Lỗi khi map dữ liệu API sang Entity cho Identity: {IdentityId}", identity?.identity_id);
                }
            }

            return entities;
        }

        /// <summary>
        /// Map dữ liệu từ API sang Entity
        /// </summary>
        /// <param name="identity">Dữ liệu identity từ API</param>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Entity</returns>
        private RawGmvMaxIdentitiesEntity MapApiDataToEntity(dynamic identity, string advertiserId, string bcId)
        {
            var entity = new RawGmvMaxIdentitiesEntity(Guid.NewGuid())
            {
                BcId = bcId,
                AdvertiserId = advertiserId,
                IdentityId = identity.identity_id?.ToString() ?? string.Empty,
                IdentityType = identity.identity_type?.ToString() ?? string.Empty,
                DisplayName = identity.display_name?.ToString(),
                UserName = identity.user_name?.ToString(),
                ProfileImage = identity.profile_image?.ToString(),
                IdentityAuthorizedBcId = identity.identity_authorized_bc_id?.ToString(),
                StoreId = identity.store_id?.ToString(),
                LiveGmvMaxAvailable = identity.live_gmv_max_available ?? false,
                ProductGmvMaxAvailable = identity.product_gmv_max_available ?? false,
                IsRunningCustomShopAds = identity.is_running_custom_shop_ads ?? false,
                UnavailableReason = identity.unavailable_reason?.ToString()
            };

            return entity;
        }

        /// <summary>
        /// Cập nhật entity từ dữ liệu mới
        /// </summary>
        /// <param name="existingEntity">Entity hiện tại</param>
        /// <param name="newEntity">Entity mới</param>
        private void UpdateEntityFromNewData(RawGmvMaxIdentitiesEntity existingEntity, RawGmvMaxIdentitiesEntity newEntity)
        {
            existingEntity.UpdateFromNewData(newEntity);
        }

        /// <summary>
        /// Cập nhật thống kê kết quả
        /// </summary>
        /// <param name="entity">Entity</param>
        /// <param name="result">Kết quả đồng bộ</param>
        private void UpdateResultStatistics(RawGmvMaxIdentitiesEntity entity, GmvMaxIdentitiesSyncResult result)
        {
            // Count by identity type
            if (!string.IsNullOrEmpty(entity.IdentityType))
            {
                if (result.IdentityTypeCount.ContainsKey(entity.IdentityType))
                    result.IdentityTypeCount[entity.IdentityType]++;
                else
                    result.IdentityTypeCount[entity.IdentityType] = 1;
            }

            // Count availability
            if (entity.ProductGmvMaxAvailable)
                result.ProductGmvMaxAvailableCount++;

            if (entity.LiveGmvMaxAvailable)
                result.LiveGmvMaxAvailableCount++;

            if (!entity.ProductGmvMaxAvailable && !entity.LiveGmvMaxAvailable)
                result.UnavailableCount++;

            if (entity.IsRunningCustomShopAds)
                result.RunningCustomShopAdsCount++;

            // Count by unavailable reason
            if (!string.IsNullOrEmpty(entity.UnavailableReason))
            {
                if (result.UnavailableReasonCount.ContainsKey(entity.UnavailableReason))
                    result.UnavailableReasonCount[entity.UnavailableReason]++;
                else
                    result.UnavailableReasonCount[entity.UnavailableReason] = 1;
            }
        }

        /// <summary>
        /// Tạo mock data cho testing - cần thay thế bằng API call thực tế
        /// </summary>
        /// <returns>Danh sách mock identity data</returns>
        private List<dynamic> CreateMockIdentityList()
        {
            // Mock data dựa trên JSON response bạn cung cấp
            var mockData = new List<dynamic>
            {
                new
                {
                    display_name = "Test Creator 1",
                    identity_authorized_bc_id = "123456789",
                    identity_id = "identity_001",
                    identity_type = "BC_AUTH_TT",
                    is_running_custom_shop_ads = false,
                    live_gmv_max_available = false,
                    product_gmv_max_available = true,
                    profile_image = "https://example.com/profile1.jpg",
                    unavailable_reason = "OCCUPIED",
                    user_name = "testcreator1"
                },
                new
                {
                    display_name = "Test Creator 2",
                    identity_authorized_bc_id = "123456789",
                    identity_id = "identity_002",
                    identity_type = "BC_AUTH_TT",
                    is_running_custom_shop_ads = false,
                    live_gmv_max_available = true,
                    product_gmv_max_available = true,
                    profile_image = "https://example.com/profile2.jpg",
                    user_name = "testcreator2"
                },
                new
                {
                    identity_id = "identity_003",
                    identity_type = "TTS_TT",
                    is_running_custom_shop_ads = false,
                    live_gmv_max_available = false,
                    product_gmv_max_available = false,
                    store_id = "store_123",
                    unavailable_reason = "UNAUTHORIZED"
                }
            };

            return mockData.Cast<dynamic>().ToList();
        }

        /// <summary>
        /// TODO: Thay thế method này bằng API call thực tế
        /// Gọi TikTok Business API để lấy danh sách identities
        ///
        /// API endpoint có thể là:
        /// - GET /open_api/v1.3/gmv_max/identities/get/
        /// - hoặc endpoint tương tự trong TikTok Business API
        ///
        /// Request parameters:
        /// - advertiser_id: ID của advertiser
        /// - access_token: Token xác thực
        ///
        /// Response format sẽ giống như JSON bạn cung cấp:
        /// {
        ///   "code": 0,
        ///   "message": "OK",
        ///   "request_id": "...",
        ///   "data": {
        ///     "identity_list": [...]
        ///   }
        /// }
        /// </summary>
        /// <param name="tikTokClient">TikTok API client</param>
        /// <param name="advertiserId">ID của advertiser</param>
        /// <returns>Response từ API</returns>
        private async Task<dynamic> CallRealIdentitiesApiAsync(dynamic tikTokClient, string advertiserId)
        {
            // TODO: Implement actual API call
            // Example:
            // var response = await tikTokClient.GMVMax.GetIdentitiesAsync(advertiserId);
            // return response;

            throw new NotImplementedException("Cần implement API call thực tế để lấy identities từ TikTok Business API");
        }
    }
}
