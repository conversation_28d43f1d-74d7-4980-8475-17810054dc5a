@page
@using Volo.Abp.Account.Settings
@using Volo.Abp.Settings
@model TikTok.Web.Pages.Account.CustomRegisterModel
@using Microsoft.AspNetCore.Mvc.Localization
@using Volo.Abp.Account.Localization
@inject IHtmlLocalizer<AccountResource> L
@inject Volo.Abp.Settings.ISettingProvider SettingProvider
@{
    ViewData["Title"] = L["Register"];
    Layout = null;
}

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - TikTok Ads Manager</title>
    
    <!-- CSS Files -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="stylesheet" href="/css/account/register.css" />
</head>
<body class="register-page">

<div class="login-container">
    <!-- Register Form -->
    <div class="login-left-section">
        <!-- Logo Section -->
        <div class="logo-section">
            <img src="/images/logo/leptonx/logo-dark.png" alt="Logo" class="logo-image" />
        </div>
        
        <!-- Subtitle -->
        <p class="subtitle">Tạo tài khoản để bắt đầu tối ưu chiến dịch GMV Max của bạn</p>
        
        <!-- Register Form Card -->
        <div class="login-form-section">
            <!-- Form Header -->
            <div class="form-header">
                <h1 class="form-title">Đăng ký</h1>
            </div>
            
            @if (Model.IsExternalLoginOnly)
            {
                <div class="alert alert-info">
                    <p>@L["ExternalProviderOnly"]</p>
                </div>
            }
            else
            {
                <form action="/Account/Register" method="post">
                    @Html.AntiForgeryToken()
                    <input name="ReturnUrl" type="hidden" value="@Model.ReturnUrl" />
                    <input name="ReturnUrlHash" type="hidden" value="@Model.ReturnUrlHash" />
                    
                    <div class="form-group">
                        <label for="UserName" class="form-label">Tên đăng nhập</label>
                        <input name="Input.UserName" id="UserName" class="form-control" placeholder="Nhập tên đăng nhập" required />
                        <span class="text-danger" id="username-error"></span>
                    </div>
                    
                    <div class="form-group">
                        <label for="EmailAddress" class="form-label">Địa chỉ email</label>
                        <input name="Input.EmailAddress" id="EmailAddress" type="email" class="form-control" placeholder="Nhập địa chỉ email" required />
                        <span class="text-danger" id="email-error"></span>
                    </div>
                    
                    <div class="form-group">
                        <label for="Password" class="form-label">Mật khẩu</label>
                        <input name="Input.Password" id="Password" type="password" class="form-control" placeholder="Nhập mật khẩu" required />
                        <span class="text-danger" id="password-error"></span>
                    </div>
                    
                    <div class="form-group">
                        <label for="ConfirmPassword" class="form-label">Xác nhận mật khẩu</label>
                        <input name="Input.ConfirmPassword" id="ConfirmPassword" type="password" class="form-control" placeholder="Nhập lại mật khẩu" required />
                        <span class="text-danger" id="confirm-password-error"></span>
                    </div>
                    
                    <button type="submit" class="btn-login">
                        Đăng ký
                    </button>
                </form>
                
                <div class="register-section">
                    <span>Đã có tài khoản? </span>
                    <a href="@Url.Page("./Login", new {returnUrl = Model.ReturnUrl, returnUrlHash = Model.ReturnUrlHash})" class="register-link">Đăng nhập ngay</a>
                </div>
            }
            
            @if (Model.VisibleExternalProviders.Any())
            {
                <div style="margin-top: 24px; text-align: center;">
                    <h6 style="color: #666; margin-bottom: 16px; font-size: 14px;">Hoặc đăng ký bằng</h6>
                    <form action="/Account/ExternalLogin" method="post">
                        @Html.AntiForgeryToken()
                        <input name="ReturnUrl" type="hidden" value="@Model.ReturnUrl" />
                        <input name="ReturnUrlHash" type="hidden" value="@Model.ReturnUrlHash" />
                        @foreach (var provider in Model.VisibleExternalProviders)
                        {
                            <button type="submit" style="margin: 8px; padding: 16px; border: 1px solid #e5e5e5; background: #ffffff; border-radius: 8px;" name="provider" value="@provider.AuthenticationScheme">
                                @provider.DisplayName
                            </button>
                        }
                    </form>
                </div>
            }
        </div>
    </div>
</div>

<!-- JavaScript Files -->
<script src="/js/account/register.js"></script>

</body>
</html>
