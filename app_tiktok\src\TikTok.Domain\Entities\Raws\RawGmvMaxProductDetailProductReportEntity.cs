using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using TikTok.Enums;
using Volo.Abp.Domain.Entities.Auditing;

namespace TikTok.Entities
{
    /// <summary>
    /// Entity đại diện cho báo cáo chi tiết cấp sản phẩm của Product GMV Max Campaign. 
    /// Lưu trữ thông tin chi tiết về hiệu suất từng sản phẩm trong chiến dịch Product GMV Max, 
    /// bao gồm thông tin sản phẩm, trạng thái và các metrics hiệu suất theo thời gian.
    /// </summary>
    public class RawGmvMaxProductDetailProductReportEntity : AuditedEntity<Guid>
    {
        public RawGmvMaxProductDetailProductReportEntity()
        {

        }

        public RawGmvMaxProductDetailProductReportEntity(Guid id) : base(id)
        {
        }

        /// <summary>
        /// ID Business Center
        /// </summary>
        [Required]
        [StringLength(100)]
        public string BcId { get; set; }

        /// <summary>
        /// ID nhà quảng cáo
        /// </summary>
        [Required]
        [StringLength(100)]
        public string AdvertiserId { get; set; }

        /// <summary>
        /// ID TikTok Shop
        /// </summary>
        [Required]
        [StringLength(100)]
        public string StoreId { get; set; }

        /// <summary>
        /// ID chiến dịch Product GMV Max
        /// </summary>
        [Required]
        [StringLength(100)]
        public string CampaignId { get; set; }

        /// <summary>
        /// ID sản phẩm SPU
        /// </summary>
        [Required]
        [StringLength(100)]
        public string ItemGroupId { get; set; }

        /// <summary>
        /// Tên sản phẩm
        /// </summary>
        [StringLength(500)]
        public string? ProductName { get; set; }

        /// <summary>
        /// URL hình ảnh sản phẩm
        /// </summary>
        [StringLength(1000)]
        public string? ProductImageUrl { get; set; }

        /// <summary>
        /// Trạng thái sản phẩm (available, unavailable)
        /// </summary>
        public ProductStatus? ProductStatus { get; set; }

        /// <summary>
        /// Chế độ tối ưu (CUSTOM: Target ROI, NO_BID: Maximum delivery)
        /// </summary>
        public ProductCampaignBidType? BidType { get; set; }

        /// <summary>
        /// Số lượng đơn hàng SKU cá nhân cho sản phẩm này
        /// </summary>
        public int? Orders { get; set; }

        /// <summary>
        /// Tổng doanh thu gộp từ sản phẩm này
        /// </summary>
        [Column(TypeName = "decimal(15,2)")]
        public decimal? GrossRevenue { get; set; }

        /// <summary>
        /// Chi phí trung bình mỗi đơn hàng (Cost Per Order)
        /// </summary>
        [Column(TypeName = "decimal(15,2)")]
        public decimal? CostPerOrder { get; set; }

        /// <summary>
        /// Tỷ lệ hoàn vốn (Return on Investment - ROI)
        /// </summary>
        [Column(TypeName = "decimal(15,2)")]
        public decimal? ROI { get; set; }

        /// <summary>
        /// Tổng chi phí quảng cáo
        /// </summary>
        [Column(TypeName = "decimal(15,2)")]
        public decimal? Cost { get; set; }

        /// <summary>
        /// Tiền tệ - Mã tiền tệ, ví dụ: USD
        /// </summary>
        [Required]
        [StringLength(10)]
        public string Currency { get; set; }

        /// <summary>
        /// Ngày giờ tổng hợp báo cáo, UTC format yyyy-MM-dd HH:mm:ss (theo giờ)
        /// </summary>
        [Required]
        public DateTime Date { get; set; }

        /// <summary>
        /// Kiểm tra xem entity có thay đổi so với entity khác không
        /// </summary>
        /// <param name="other">Entity khác để so sánh</param>
        /// <returns>True nếu có thay đổi</returns>
        public bool HasChanged(RawGmvMaxProductDetailProductReportEntity other)
        {
            if (other == null) return true;

            return BcId != other.BcId ||
                   AdvertiserId != other.AdvertiserId ||
                   StoreId != other.StoreId ||
                   CampaignId != other.CampaignId ||
                   ItemGroupId != other.ItemGroupId ||
                   ProductName != other.ProductName ||
                   ProductImageUrl != other.ProductImageUrl ||
                   ProductStatus != other.ProductStatus ||
                   BidType != other.BidType ||
                   Orders != other.Orders ||
                   GrossRevenue != other.GrossRevenue ||
                   CostPerOrder != other.CostPerOrder ||
                   ROI != other.ROI ||
                   Cost != other.Cost ||
                   Currency != other.Currency ||
                   Date != other.Date;
        }

        /// <summary>
        /// Cập nhật dữ liệu từ dữ liệu mới
        /// </summary>
        /// <param name="newEntity">Dữ liệu mới</param>
        public void UpdateFromNewData(RawGmvMaxProductDetailProductReportEntity newEntity)
        {
            BcId = newEntity.BcId;
            AdvertiserId = newEntity.AdvertiserId;
            StoreId = newEntity.StoreId;
            CampaignId = newEntity.CampaignId;
            ItemGroupId = newEntity.ItemGroupId;
            ProductName = newEntity.ProductName;
            ProductImageUrl = newEntity.ProductImageUrl;
            ProductStatus = newEntity.ProductStatus;
            BidType = newEntity.BidType;
            Orders = newEntity.Orders;
            GrossRevenue = newEntity.GrossRevenue;
            CostPerOrder = newEntity.CostPerOrder;
            ROI = newEntity.ROI;
            Cost = newEntity.Cost;
            Currency = newEntity.Currency;
            Date = newEntity.Date;
        }
    }
}