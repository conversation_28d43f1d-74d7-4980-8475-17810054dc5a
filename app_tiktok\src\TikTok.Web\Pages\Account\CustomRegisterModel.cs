using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Options;
using Volo.Abp.Account;
using Volo.Abp.Account.Web;
using Volo.Abp.Account.Web.Pages.Account;
using Volo.Abp.Identity;

namespace TikTok.Web.Pages.Account
{
    public class CustomRegisterModel : RegisterModel
    {
        public CustomRegisterModel(
            IAccountAppService accountAppService,
            IAuthenticationSchemeProvider schemeProvider,
            IOptions<AbpAccountOptions> accountOptions,
            IdentityDynamicClaimsPrincipalContributorCache identityDynamicClaimsPrincipalContributorCache)
            : base(accountAppService, schemeProvider, accountOptions, identityDynamicClaimsPrincipalContributorCache)
        {
        }

        // Có thể override các method hoặc thêm properties mới ở đây
        // Ví dụ:
        // public override async Task<IActionResult> OnPostAsync()
        // {
        //     // Custom logic trước khi register
        //     return await base.OnPostAsync();
        // }
    }
}
